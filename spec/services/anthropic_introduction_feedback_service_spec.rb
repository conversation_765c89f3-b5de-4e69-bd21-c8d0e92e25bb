require "rails_helper"

RSpec.describe AnthropicIntroductionFeedbackService do
  describe "#call" do
    let(:content) { "I'm StoryCoa<PERSON>. I help people become better storytellers" }

    it "calls Anthropic API and returns generated text" do
      service = AnthropicIntroductionFeedbackService.new

      # Stub the Anthropic API call to return a specific response
      allow(service).to receive(:generate_text).and_return("Generated feedback")

      result = service.call(content)

      expect(result).to eq("Generated feedback")
    end
  end
end
