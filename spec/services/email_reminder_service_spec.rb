# spec/services/email_reminder_service_spec.rb

require "rails_helper"

RSpec.describe EmailReminderService do
  describe "#call" do
    let!(:non_premium_user) { create(:user, practice_frequency: "daily", practice_time_of_day: "morning", email: "<EMAIL>") }
    let!(:morning_user) { create(:user, practice_frequency: "daily", practice_time_of_day: "morning", email: "<EMAIL>", premium_subscriber: true) }
    let!(:afternoon_user) { create(:user, practice_frequency: "weekly", practice_time_of_day: "afternoon", email: "<EMAIL>", premium_subscriber: true) }
    let!(:evening_user) { create(:user, practice_frequency: "never", practice_time_of_day: "evening", email: "<EMAIL>", premium_subscriber: true) }

    before do
      allow(ReminderMailers).to receive(:h4l_mailer).and_call_original
      allow(Date).to receive(:today).and_return(today)
      allow(Time).to receive(:now).and_return(current_time)
    end

    context "when time of day is morning" do
      let(:current_time) { Time.new(2024, 6, 24, 8, 0, 0) } # 8 AM
      let(:today) { Date.new(2024, 6, 24) } # Any non-Sunday date

      it "sends daily reminders to morning users" do
        expect(ReminderMailers).to receive(:h4l_mailer).with(morning_user).and_call_original
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(afternoon_user)
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(evening_user)
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(non_premium_user)

        # expect(Rails.logger).to receive(:info).with("Sending homework_for_life reminder to #{morning_user.email}")
        # expect(Rails.logger).to receive(:info).with("Not sending homework_for_life reminder to #{evening_user.email}")

        EmailReminderService.new.call
      end
    end

    context "when time of day is afternoon and today is Sunday" do
      let(:current_time) { Time.new(2024, 6, 23, 14, 0, 0) } # 2 PM on a Sunday
      let(:today) { Date.new(2024, 6, 23) } # A Sunday date

      it "sends weekly reminders to afternoon users" do
        expect(ReminderMailers).to receive(:h4l_mailer).with(afternoon_user).and_call_original
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(morning_user)
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(evening_user)
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(non_premium_user)

        # expect(Rails.logger).to receive(:info).with("Sending homework_for_life reminder to #{afternoon_user.email}")
        # expect(Rails.logger).to receive(:info).with("Not sending homework_for_life reminder to #{evening_user.email}")

        EmailReminderService.new.call
      end
    end

    context "when practice frequency is never" do
      let(:current_time) { Time.new(2024, 6, 24, 19, 0, 0) } # 7 PM
      let(:today) { Date.new(2024, 6, 24) } # Any non-Sunday date

      it "does not send the daily reminders to evening users or non-premium users" do
        # Stub User.where to return only the relevant user for this context
        allow(User).to receive(:where).with(practice_time_of_day: "evening").and_return([evening_user])
        # Removed the general .and_call_original stub which was overriding the specific one above

        expect(ReminderMailers).not_to receive(:h4l_mailer).with(evening_user)
        expect(ReminderMailers).not_to receive(:h4l_mailer).with(non_premium_user)

        # This expectation should now pass because the service will only process evening_user
        expect(Rails.logger).to receive(:info).with("Not sending homework_for_life reminder to #{evening_user.email}")

        EmailReminderService.new.call
      end
    end
  end
end
