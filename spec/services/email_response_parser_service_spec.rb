# spec/services/email_response_parser_service_spec.rb

require 'rails_helper'

RSpec.describe EmailResponseParserService do
  describe '#call' do
    let(:service) { EmailResponseParserService.new }

    context 'when the email contains a reply to the original email' do
      it 'parses the email response successfully' do
        email_text = <<~EMAIL
        Lost my tennis match, but felt better than in other games I have won as I played better. 
        Party after that. All day, I still felt something was missing.

        On Sat, Jul 22, 2023 at 9:00 PM <<EMAIL>> wrote:
        Your daily reminder to become more storyworthy.
        Take 5 minutes and reply to this email with a memory from today.
        EMAIL

        expected_text = <<~EXTRACTED_TEXT
        Lost my tennis match, but felt better than in other games I have won as I played better. 
        Party after that. All day, I still felt something was missing.
        EXTRACTED_TEXT

        result = service.call(email_text)
        expect(result).to eq(expected_text.strip)
      end
    end

    context 'when the email does not contain a reply to the original email' do
      it 'parses the email successfully' do
        email_text = <<~EMA<PERSON>
        Lost my tennis match, but felt better than in other games I have won as I played better. 
        Party after that. All day, I still felt something was missing.
        EMA<PERSON>

        expected_text = <<~EXTRACTED_TEXT
        Lost my tennis match, but felt better than in other games I have won as I played better. 
        Party after that. All day, I still felt something was missing.
        EXTRACTED_TEXT

        result = service.call(email_text)
        expect(result).to eq(expected_text.strip)
      end
    end
  end
end
