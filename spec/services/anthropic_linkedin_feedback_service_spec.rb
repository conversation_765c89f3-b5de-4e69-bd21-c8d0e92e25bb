require 'rails_helper'

RSpec.describe AnthropicLinkedinFeedbackService do
  let(:service) { described_class.new }
  let(:story_content) { "This is my draft post about achieving a milestone." }
  let(:anthropic_client_double) { instance_double(Anthropic::Client) }
  let(:raw_feedback_text) { "Here's StoryCoach's feedback: Great start! Consider adding a stronger hook. Maybe use \\\"this\\\" format.\\nTry a call to action.\\tGood job." }
  let(:sanitized_feedback_text) { "Here's Story<PERSON>oach's feedback: Great start! Consider adding a stronger hook. Maybe use \"this\" format.\nTry a call to action.\tGood job." }
  let(:api_response_success) do
    {
      "content" => [
        { "type" => "text", "text" => raw_feedback_text }
      ]
    }
  end
  let(:api_response_error) { StandardError.new("API connection failed") }
  let(:expected_system_prompt) { described_class::SYSTEM_PROMPT }
  let(:expected_user_prompt) do
    "A strong LinkedIn post has the following elements: " +
    "(1) A strong hook to engage the reader in the first line, " +
    "(2) Valuable insights or lessons that benefit the reader, " +
    "(3) Personal authenticity that shows your unique voice, " +
    "(4) Clear formatting with short paragraphs and white space, and " +
    "(5) A simple call to action or thought-provoking question at the end. " +
    "Provide feedback on the following LinkedIn post draft: #{story_content}"
  end

  before do
    allow(Anthropic::Client).to receive(:new).and_return(anthropic_client_double)
    allow(anthropic_client_double).to receive(:messages).and_return(api_response_success)
  end

  describe '#call' do
    it 'calls the Anthropic API with the correct parameters' do
      expect(anthropic_client_double).to receive(:messages).with(
        parameters: {
          model: "claude-3-7-sonnet-20250219",
          system: expected_system_prompt,
          messages: [
            { role: "user", content: expected_user_prompt }
          ],
          temperature: 0,
          max_tokens: 1000
        }
      ).and_return(api_response_success)

      service.call(story_content)
    end

    it 'returns sanitized feedback on success' do
      expect(Rails.logger).to receive(:info).with("AnthropicLinkedinFeedbackService: #{raw_feedback_text}")
      result = service.call(story_content)
      expect(result).to eq(sanitized_feedback_text)
    end

    it 'correctly sanitizes feedback text' do
      # This test implicitly covers sanitize_feedback via the main success case,
      # but we can be more explicit if needed, though testing private methods directly is often discouraged.
      # Let's rely on the main success case checking the final output.
      result = service.call(story_content)
      expect(result).not_to include('\\"')
      expect(result).not_to include("\\'")
      expect(result).not_to include('\\n')
      expect(result).not_to include('\\t')
      expect(result).to include('"')
      expect(result).to include("'") # Assuming the client might return single quotes too
      expect(result).to include("\n")
      expect(result).to include("\t")
    end

    context 'when the API call fails' do
      before do
        allow(anthropic_client_double).to receive(:messages).and_raise(api_response_error)
        allow($stdout).to receive(:puts) # Suppress console output during test
      end

      it 'logs the error via puts and returns nil' do
        # The logger IS called even on error, but with a nil generated_text
        expect(Rails.logger).to receive(:info).with("AnthropicLinkedinFeedbackService: ")
        expect($stdout).to receive(:puts).with("Error generating text: API connection failed")
        expect(service.call(story_content)).to be_nil
      end
    end

    context 'when the API response is nil' do
      before do
        allow(anthropic_client_double).to receive(:messages).and_return(nil)
        allow($stdout).to receive(:puts) # Suppress console output
      end

      it 'handles nil response gracefully and returns nil' do
         # Accessing ["content"] on nil will raise NoMethodError, caught by rescue
        # The logger IS called even on error, but with a nil generated_text
        expect(Rails.logger).to receive(:info).with("AnthropicLinkedinFeedbackService: ")
        expect($stdout).to receive(:puts).with(/Error generating text: undefined method `\[\]' for nil/)
        expect(service.call(story_content)).to be_nil
      end
    end

    context 'when the API response content is missing text' do
      let(:api_response_malformed) do
        { "content" => [{ "type" => "other" }] } # Missing 'text' key
      end
      before do
        allow(anthropic_client_double).to receive(:messages).and_return(api_response_malformed)
        allow($stdout).to receive(:puts) # Suppress console output
      end

      it 'handles malformed response gracefully and returns nil' do
        # Accessing ["text"] on nil will return nil, no error raised in generate_text
        # generate_text returns nil, logger is called with nil
        expect(Rails.logger).to receive(:info).with("AnthropicLinkedinFeedbackService: ")
        # Since no error is raised in generate_text, puts is NOT called in this specific case
        expect($stdout).not_to receive(:puts)
        expect(service.call(story_content)).to be_nil
      end
    end
  end
end
