require "rails_helper"

RSpec.describe FirstBadgeAllocationService do
  describe "#call" do
    let(:user) { create(:user) }

    it "creates a new badge for the user and sends a mail" do
      expect {
        described_class.new(user).call
      }.to change { user.badges.count }.by(1)

      # Check that the first badge email job has been enqueued
      expect { described_class.new(user).call }.to have_enqueued_job(ActionMailer::MailDeliveryJob).with(
        'UserMailer', 'first_badge', 'deliver_now', { args: [user] }
      )
    end

    it "creates a badge with the correct attributes" do
      service = described_class.new(user)
      service.call

      badge = user.badges.last

      expect(badge.name).to eq("Inkling Beginnings")
      expect(badge.description).to eq("Embark on your storytelling journey as you take your first steps into the world of imagination and creativity.")
      expect(badge.icon).to eq("Shuttle_Badge_Line.png")
    end
  end
end
