require "rails_helper"

RSpec.describe PointsAndLevelAllocationService, type: :service do
  describe "#call" do
    let(:non_premium_user) { create(:user, points: 0, level: 0) } # level 0 and non-premium subscriber.
    let(:premium_user) { create(:user, points: 0, level: 0, ai_feedback_credits: 3, premium_subscriber: true) }

    before do
      allow(UserMailer).to receive(:next_level).and_call_original
    end

    it "updates premium user's points and not the level if points not enough" do
      service = described_class.new(premium_user, points: 4)
      service.call

      premium_user.reload

      expect(premium_user.points).to eq(4)
      expect(premium_user.level).to eq(1)
      expect(premium_user.ai_feedback_credits).to eq(3)
    end

    it "updates non-premium user's points and not the level if points not enough" do
      service = described_class.new(non_premium_user, points: 4)
      service.call

      non_premium_user.reload

      expect(non_premium_user.points).to eq(4)
      expect(non_premium_user.level).to eq(1)
      expect(non_premium_user.ai_feedback_credits).to eq(0)
    end

    it "updates premium user's points, level, credits, and sends next_level email if points are enough" do
      expect(UserMailer).to receive(:next_level).with(premium_user).and_call_original

      service = described_class.new(premium_user, points: 5)
      service.call

      premium_user.reload
      expect(premium_user.points).to eq(5)
      expect(premium_user.level).to eq(1)
      expect(premium_user.ai_feedback_credits).to eq(3)
    end

    it "updates non-premium user's points, level, and notcredits, and sends next_level email if points are enough" do
      expect(UserMailer).to receive(:next_level).with(non_premium_user).and_call_original

      service = described_class.new(non_premium_user, points: 5)
      service.call

      non_premium_user.reload
      expect(non_premium_user.points).to eq(5)
      expect(non_premium_user.level).to eq(1)
      expect(non_premium_user.ai_feedback_credits).to eq(0)
    end
  end
end