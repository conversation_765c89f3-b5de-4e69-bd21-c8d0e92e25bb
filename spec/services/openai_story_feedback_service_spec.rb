require "rails_helper"

RSpec.describe OpenaiStoryFeedbackService do
  describe "#call" do
    let(:story_content) { "Once upon a time..." }

    it "calls the OpenAI API and returns the generated feedback" do
      # Stubbing the OpenAI::Client to avoid actual API calls
      openai_client_double = instance_double(OpenAI::Client)
      allow(OpenAI::Client).to receive(:new).and_return(openai_client_double)

      # Stubbing the chat method to simulate the API response
      allow(openai_client_double).to receive(:chat).and_return({
        "choices" => [{ "message" => { "content" => "Generated feedback from OpenAI" } }]
      })

      service = OpenaiStoryFeedbackService.new
      generated_feedback = service.call(story_content)

      expect(generated_feedback).to eq("Generated feedback from OpenAI")
    end
  end
end
