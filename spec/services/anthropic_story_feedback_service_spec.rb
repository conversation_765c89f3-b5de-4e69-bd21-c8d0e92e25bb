require "rails_helper"

RSpec.describe AnthropicStoryFeedbackService do
  describe "#call" do
    let(:story_content) { "Once upon a time..." }

    it "calls Anthropic API and returns generated text" do
      service = AnthropicStoryFeedbackService.new

      # Stub the Anthropic API call to return a specific response
      allow(service).to receive(:generate_text).and_return("Generated feedback")

      result = service.call(story_content)

      expect(result).to eq("Generated feedback")
    end
  end
end
