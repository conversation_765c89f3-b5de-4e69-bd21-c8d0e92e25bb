require "rails_helper"

RSpec.describe FirstExerciseBadgeAllocationService do
  describe "#call" do
    let(:user) { create(:user) } # Assuming you have a User factory set up

    it "creates a new badge for the user and sends a mail" do
      expect {
        described_class.new(user).call
      }.to change { user.badges.count }.by(1)

      expect { described_class.new(user).call }.to have_enqueued_job(ActionMailer::MailDeliveryJob).with(
        'UserMailer', 'first_exercise_badge', 'deliver_now', { args: [user] }
      )
    end

    it "creates a badge with the correct attributes" do
      service = described_class.new(user)
      service.call

      badge = user.badges.last

      expect(badge.name).to eq("Imagination Awakening")
      expect(badge.description).to eq("By completing your first exercise to hone your skills as a storyteller, you have taken the next step in unleashing your creativity.")
      expect(badge.icon).to eq("Growth_Badge_Line.png")
    end
  end
end
