FactoryBot.define do
  factory :user do
    name { Faker::Name.name }
    email { Faker::Internet.email }
    password { Faker::Internet.password }
  end

  factory :story do
    title { Faker::Book.title }
    content { Faker::Quote.matz }
    category { "general" }

    trait :origin do
      title { nil }
      category { "origin" }
    end
  end

  factory :exercise do
    note { Faker::Lorem.sentence }
    note_date { Date.today }
    category { "homework_for_life" }
    association :user
  end
end