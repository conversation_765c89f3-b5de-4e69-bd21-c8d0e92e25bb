require "rails_helper"

RSpec.describe ReminderMailers, type: :mailer do
  let(:user) { create(:user, email: "<EMAIL>") }

  after do
    ActionMailer::Base.deliveries.clear
  end

  describe "#h4l_mailer" do
    before do
      user.exercises.create(
        note: Faker::Quote.famous_last_words,
        note_date: Time.now,
        category: "homework_for_life",
      )
    end

    let(:mail) { ReminderMailers.h4l_mailer(user) }

    it "renders the headers" do
      expect(mail.subject).to eq("StoryCoach's reminder to save a memory from the day - #{Time.now.strftime("%m/%d/%Y")}")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
      expect(mail.reply_to).to eq([ENV["POSTMARK_INBOUND_EMAIL"]])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include("#{user.first_name}!")
    end

    it "captures and renders a highlighted memory on days that are multiples of 5" do
      allow(Date).to receive(:today).and_return(Date.new(2023, 8, 15))

      expect(mail.body.encoded).to include("Need a jog down memory lane?")
    end

    # it "includes link to Crash and Burn exercise on days that are multiples of 23" do
    #   allow(Date).to receive(:today).and_return(Date.new(2023, 8, 23))

    #   expect(mail.body.encoded).to include("Ready to flex your writing muscles?")
    # end

    # it "includes link to Random Word exercise on days that are multiples of 7" do
    #   allow(Date).to receive(:today).and_return(Date.new(2023, 8, 14))

    #   expect(mail.body.encoded).to include("Ready to work on your writing muscles?")
    # end

    # it "includes link to Prompt Inspiration exercise on days that are multiples of 8" do
    #   allow(Date).to receive(:today).and_return(Date.new(2023, 8, 16))

    #   expect(mail.body.encoded).to include("Looking for ideas for what to write about?")
    # end
  end

  describe "#weekly_stats" do
    before do
      user.exercises.create(
        note: Faker::Quote.famous_last_words,
        note_date: 1.day.ago,
        category: "homework_for_life",
      )
    end

    let(:mail) { ReminderMailers.weekly_stats(user) }

    it "renders the headers" do
      expect(mail.subject).to eq("🏁 See your StoryCoach stats!")
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
      expect(mail.reply_to).to eq([ENV["POSTMARK_INBOUND_EMAIL"]])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include("#{user.first_name}, check out your weekly stats!")
    end
  end
end
