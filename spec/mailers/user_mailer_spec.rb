require "rails_helper"

RSpec.describe UserMailer, type: :mailer do
  let(:user) { create(:user, name: Faker::Name.name, email: "<EMAIL>") }

  after do
    ActionMailer::Base.deliveries.clear
  end

  describe "#welcome" do
    let(:mail) { UserMailer.welcome(user) }

    it "renders the headers" do
      expect(mail.subject).to eq(I18n.t("user_mailer.welcome.subject"))
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(
        "#{user.first_name}, thanks for signing up"
      )
    end

    # it "mentions the credits available " do
    #   expect(mail.body.encoded).to include(
    #     "You have been allocated #{user.ai_feedback_credits} free credits!"
    #   )
    # end
  end

  describe "#first_badge" do
    let(:mail) { UserMailer.first_badge(user) }

    it "renders the headers" do
      expect(mail.subject).to eq(I18n.t("user_mailer.first_badge.subject"))
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(
        "#{user.first_name}, congratulations!"
      )
    end
  end

  describe "#first_exercise_badge" do
    let(:mail) { UserMailer.first_exercise_badge(user) }

    it "renders the headers" do
      expect(mail.subject).to eq(I18n.t("user_mailer.first_exercise_badge.subject"))
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(
        "#{user.first_name}, congratulations!"
      )
    end
  end

  describe "#first_story_badge" do
    let(:mail) { UserMailer.first_story_badge(user) }

    it "renders the headers" do
      expect(mail.subject).to eq(I18n.t("user_mailer.first_story_badge.subject"))
      expect(mail.to).to eq([user.email])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to include(
        "#{user.first_name}, congratulations!"
      )
    end
  end
end
