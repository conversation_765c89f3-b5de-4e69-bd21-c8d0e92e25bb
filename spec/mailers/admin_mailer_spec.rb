require "rails_helper"

RSpec.describe AdminMailer, type: :mailer do
  describe "new_user" do
    let(:user) { create(:user, email: "<EMAIL>", name: "New User") }
    let(:mail) { AdminMailer.new_user(user) }

    it "renders the headers" do
      expect(mail.subject).to eq("StoryCoach.app has a new user")
      expect(mail.to).to eq(["<EMAIL>"])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "renders the body" do
      expect(mail.body.encoded).to match("A new user has signed up with the email address:")
    end
  end
end