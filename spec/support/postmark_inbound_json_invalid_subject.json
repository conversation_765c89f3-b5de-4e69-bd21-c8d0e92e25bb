{"FromName": "Postmarkapp Support", "MessageStream": "inbound", "From": "<EMAIL>", "FromFull": {"Email": "<EMAIL>", "Name": "Postmarkapp Support", "MailboxHash": ""}, "To": "\"Firstname Lastname\" <<EMAIL>>", "ToFull": [{"Email": "<EMAIL>", "Name": "Firstname Lastname", "MailboxHash": "SampleHash"}], "Cc": "\"First Cc\" <<EMAIL>>, <EMAIL>>", "CcFull": [{"Email": "<EMAIL>", "Name": "First Cc", "MailboxHash": ""}, {"Email": "<EMAIL>", "Name": "", "MailboxHash": ""}], "OriginalRecipient": "<EMAIL>", "Subject": "This is a bad subject line", "MessageID": "73e6d360-66eb-11e1-8e72-a8904824019b", "ReplyTo": "<EMAIL>", "MailboxHash": "SampleHash", "Date": "Fri, 1 Aug 2014 16:45:32 -04:00", "TextBody": "This is a test text body.", "HtmlBody": "<html><body><p>This is a test html body.</p></body></html>", "StrippedTextReply": "This is the reply text", "Tag": "TestTag", "Headers": [{"Name": "X-Header-Test", "Value": ""}, {"Name": "X-Spam-Status", "Value": "No"}, {"Name": "X-Spam-Score", "Value": "-0.1"}, {"Name": "X-Spam-Tests", "Value": "DKIM_SIGNED,DKIM_VALID,DKIM_VALID_AU,SPF_PASS"}], "Attachments": [{"Name": "test.txt", "Content": "VGhpcyBpcyBhdHRhY2htZW50IGNvbnRlbnRzLCBiYXNlLTY0IGVuY29kZWQu", "ContentType": "text/plain", "ContentLength": 45}]}