require "rails_helper"

RSpec.describe Api::V1::UsersController, type: :controller do
  include Devise::Test::ControllerHelpers

  let(:user) { create(:user, api_key: "SECRET_SOURCEGRAPH_1", practice_frequency: "daily", practice_time_of_day: "evening") }
  let(:headers) { { "Authorization" => user.api_key } }

  describe "GET #show" do
    context "with valid api_key" do
      before do
        request.headers.merge!(headers)
        get :show, params: { id: user.api_key }
      end

      it "returns the user" do
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(user.id)
      end
    end

    context "with invalid api_key" do
      before do
        request.headers["Authorization"] = "INVALID_API_KEY"
        get :show, params: { id: "INVALID_API_KEY" }
      end

      it "returns not found" do
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)["error"]).to eq("Unauthorized")
      end
    end
  end

  describe "PATCH #update" do
    context "with valid api_key" do
      before do
        request.headers.merge!(headers)
        patch :update, params: { id: user.api_key, user: { practice_time_of_day: "morning" } }
      end

      it "updates the user" do
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["practice_time_of_day"]).to eq("morning")
      end
    end

    context "with invalid api_key" do
      before do
        request.headers["Authorization"] = "INVALID_API_KEY"
        patch :update, params: { id: "INVALID_API_KEY", user: { practice_time_of_day: "morning" } }
      end

      it "returns not found" do
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)["error"]). to eq("Unauthorized")
      end
    end

    context "with invalid parameters" do
      before do
        request.headers.merge!(headers)
        patch :update, params: { id: user.api_key, user: { practice_time_of_day: nil } }
      end

      it "returns unprocessable entity" do
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)["errors"]).to include("Practice time of day can't be blank")
      end
    end
  end
end