require "rails_helper"

RSpec.describe Api::V1::ExercisesController, type: :controller do
  include Devise::Test::ControllerHelpers

  let(:user) { create(:user, api_key: "SECRET_SOURCEGRAPH_1") }
  let(:headers) { { "Authorization" => user.api_key } }
  let(:exercise) { create(:exercise, user: user) }

  describe "GET #index" do
    context "with valid api_key" do
      before do
        request.headers.merge!(headers)
        get :index
      end

      it "returns the user's exercises" do
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body).length).to eq(user.exercises.count)
      end
    end

    context "with invalid api_key" do
      before do
        request.headers["Authorization"] = "INVALID_API_KEY"
        get :index
      end

      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)["error"]).to eq("Unauthorized")
      end
    end
  end

  describe "GET #show" do
    context "with valid api_key" do
      before do
        request.headers.merge!(headers)
        get :show, params: { id: exercise.id }
      end

      it "returns the exercise" do
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(exercise.id)
      end
    end

    context "with invalid api_key" do
      before do
        request.headers["Authorization"] = "INVALID_API_KEY"
        get :show, params: { id: exercise.id }
      end

      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)["error"]).to eq("Unauthorized")
      end
    end

    context "when exercise does not exist" do
      before do
        request.headers.merge!(headers)
        get :show, params: { id: 0 }
      end

      it "returns not found" do
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)["error"]).to eq("Exercise not found")
      end
    end
  end

  describe "POST #create" do
    context "with valid api_key" do
      before do
        request.headers.merge!(headers)
      end

      it "creates a new exercise" do
        expect {
          post :create, params: { exercise: { note: "New note", note_date: Date.today, category: "homework_for_life", photo: nil } }
        }.to change(user.exercises, :count).by(1)
        expect(response).to have_http_status(:created)
      end
    end

    context "with invalid api_key" do
      before do
        request.headers["Authorization"] = "INVALID_API_KEY"
        post :create, params: { exercise: { note: "New note", note_date: Date.today, category: "homework_for_life", photo: nil } }
      end

      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)["error"]).to eq("Unauthorized")
      end
    end

    context "with invalid parameters" do
      before do
        request.headers.merge!(headers)
        post :create, params: { exercise: { note: nil, note_date: nil, category: nil, photo: nil } }
      end

      it "returns unprocessable entity" do
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)["errors"]).to include("Note can't be blank", "Note date can't be blank", "Category can't be blank")
      end
    end
  end
end