require "rails_helper"

RSpec.describe PostmarkInboundsController, type: :controller do
  include Devise::Test::ControllerHelpers

  describe "#create" do
    it "processes incoming email and creates exercise for existing user" do
      user = create(:user, email: "<EMAIL>")
      exercise_category = "homework_for_life"
      email_response = File.read(Rails.root.join("spec/support/postmark_inbound_json.json"))

      expect(User).to receive(:find_by_email).with("<EMAIL>").and_return(user)
      expect_any_instance_of(EmailResponseParserService).to receive(:call).and_return("Some parsed note")

      expect {
        post :create, body: email_response
      }.to change(Exercise, :count).by(1)

      exercise = user.exercises.last
      expect(exercise.note_date).to eq(Date.new(2023, 8, 18).beginning_of_day)
      expect(exercise.note).to eq("Some parsed note")
      expect(exercise.category).to eq(exercise_category)

      expect(response).to have_http_status(200)
      expect(response.body).to eq("Response Saved")
    end

    it "returns a 400 response for non-existing user" do
      email_response = File.read(Rails.root.join("spec/support/postmark_inbound_json_nonexistant.json"))

      expect(User).to receive(:find_by_email).with("<EMAIL>").and_return(nil)
      expect_any_instance_of(EmailResponseParserService).not_to receive(:call)

      expect {
        post :create, body: email_response
      }.to change(Exercise, :count).by(0)

      expect(response).to have_http_status(400)
      expect(response.body).to eq("Unable to save")
    end

    it "returns a 200 response for invalid subject line and creates exercise if user found" do
      user1 = create(:user, email: "<EMAIL>")
      exercise_category = "homework_for_life"
      email_response = File.read(Rails.root.join("spec/support/postmark_inbound_json_invalid_subject.json"))

      # expect(User).to receive(:find_by_email).with("<EMAIL>").and_return(user1)
      # expect_any_instance_of(EmailResponseParserService).to receive(:call).and_return("Some parsed note")

      # expect {
      #   post :create, body: email_response
      # }.to change(Exercise, :count).by(1)

      # exercise = user.exercises.last
      # expect(exercise.note_date).to eq(Date.strptime(Time.now.strftime("%m/%d/%Y")).beginning_of_day)
      # expect(exercise.category).to eq(exercise_category)

      # expect(response).to have_http_status(200)
      # expect(response.body).to eq("Response Saved")
    end
  end
end
