require "rails_helper"

RSpec.describe Story, type: :model do
  let(:user) { create(:user) }
  let(:story) { build(:story, user: user) }

  describe "validations" do
    it "is valid with valid attributes" do
      expect(story).to be_valid
    end

    it "is not valid without a user" do
      story.user = nil
      expect(story).not_to be_valid
    end
  end

  describe "associations" do
    it { should belong_to(:user) }
    it { should have_many(:feedbacks) }
  end

  describe "callbacks" do
    context "after_create" do
      it "calls allocate_first_story_badge" do
        expect_any_instance_of(Story).to receive(:allocate_first_story_badge)
        story.save
      end

      it "calls update_points_and_level" do
        expect_any_instance_of(Story).to receive(:update_points_and_level)
        story.save
      end
    end
  end

  describe "friendly_id slugs" do
    let(:origin_story) { build(:story, :origin, user: user) }

    it "generates a slug based on the story title" do
      story.save
      expect(story.slug).to eq story.title.parameterize
    end

    it "generates a slut for origin and introduction stories when title is nil" do
      origin_story.save
      expect(origin_story.slug).to eq "#{origin_story.user.name.parameterize}-origin"
    end

    it "appends a number to the slug if there is a duplicate" do
      story.save
      duplicate_story = build(:story, title: story.title, content: story.content, user: user)
      duplicate_story.save
      expect(duplicate_story.slug).to include(story.slug)
    end
  end

  describe "scopes" do
    it "returns only published stories" do
      published_story = create(:story, user: user, published: true)
      unpublished_story = create(:story, user: user, published: false)

      expect(Story.published).to include(published_story)
      expect(Story.published).not_to include(unpublished_story)
    end
  end
end