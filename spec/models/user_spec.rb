require "rails_helper"

RSpec.describe User, type: :model do
  let(:user) { build(:user, first_name: "<PERSON>", last_name: "<PERSON><PERSON>") }

  describe 'validations' do
    it 'is valid with valid attributes' do
      expect(user).to be_valid
    end

    it 'is not valid without a practice_time_of_day' do
      user.practice_time_of_day = nil
      expect(user).not_to be_valid
      expect(user.errors[:practice_time_of_day]).to include("can't be blank")
    end

    it 'is not valid without a practice_frequency' do
      user.practice_frequency = nil
      expect(user).not_to be_valid
      expect(user.errors[:practice_frequency]).to include("can't be blank")
    end
  end

  describe "friendly_id slugs" do
    it "generates a slug based on the first and last name" do
      user.save
      expect(user.slug).to eq "john-doe"
    end

    it "appends a number to the slug if there is a duplicate" do
      user.save
      duplicate_user = build(:user, first_name: "<PERSON>", last_name: "<PERSON><PERSON>")
      duplicate_user.save
      expect(duplicate_user.slug).to match(/^john-doe-\d+$/)
    end
  end

  describe "#premium_user?" do
    it "returns true if user is an admin, has a stripe token, or is a premium subscriber" do
      user.stripe_token = "some_token"
      expect(user.premium_user?).to be true
    end

    it "returns false if user does not have any premium attributes" do
      expect(user.premium_user?).to be false
    end
  end

  describe "#has_ai_credits?" do
    it "returns true if the user has positive ai_feedback_credits" do
      user.ai_feedback_credits = 1
      expect(user.has_ai_credits?).to be true
    end

    it "returns false if the user has zero ai_feedback_credits" do
      user.ai_feedback_credits = 0
      expect(user.has_ai_credits?).to be false
    end
  end

  describe "callbacks" do
    before do
      ActiveJob::Base.queue_adapter = :test
    end

    it "allocates a first badge after create" do
      expect_any_instance_of(FirstBadgeAllocationService).to receive(:call)
      user.save
    end

    it "sends both UserMailer and AdminMailer emails after create" do
      user.save
      expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with("UserMailer", "welcome", "deliver_now", { args: [user] })
      expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with("UserMailer", "first_badge", "deliver_now", { args: [user] })
      expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with("AdminMailer", "new_user", "deliver_now", { args: [user] })
    end

    it "allocates ai feedback credits based on user premium status" do
      user.save
      expected_credits = user.premium_user? ? User::DEFAULT_AI_CREDITS_ALLOCATION : 0
      expect(user.ai_feedback_credits).to eq expected_credits
    end
  end
end
