require "rubygems"
require "sitemap_generator"

# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = "https://storycoach.app"

# Create both compressed and uncompressed versions
SitemapGenerator::Sitemap.create_index = true
SitemapGenerator::Sitemap.compress = false

SitemapGenerator::Sitemap.create do
  # Put links creation logic here.
  #
  # The root path '/' and sitemap index file are added automatically for you.
  # Links are added to the Sitemap in the order they are specified.
  #
  # Usage: add(path, options={})
  #        (default options are used if you don't specify)
  #
  # Defaults: :priority => 0.5, :changefreq => 'weekly',
  #           :lastmod => Time.now, :host => default_host
  #
  # Examples:
  #
  # Add '/articles'
  #
  #   add articles_path, :priority => 0.7, :changefreq => 'daily'
  #
  # Add all articles:
  #
  #   Article.find_each do |article|
  #     add article_path(article), :lastmod => article.updated_at
  #   end
  add "/about"
  add "/ai_feedback"
  add "/crash_and_burn"
  add "/first_last_best_worst"
  add "homework_for_life"
  add "/pricing"
  add "/privacy"
  add "/terms"
end
