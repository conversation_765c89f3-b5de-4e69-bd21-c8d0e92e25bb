test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

digital_ocean:
  service: S3
  access_key_id: <%= ENV["DO_ACCESS_KEY"] %>
  secret_access_key: <%= ENV["DO_SECRET_KEY"] %>
  region: nyc3
  bucket: <%= ENV["DO_BUCKET"] %>
  endpoint: 'https://nyc3.digitaloceanspaces.com'