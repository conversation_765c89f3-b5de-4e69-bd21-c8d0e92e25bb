require 'sidekiq/web'

Rails.application.routes.draw do
  draw :madmin
  get "/about", to: "home#about"
  get "/crash_and_burn", to: "home#crash_and_burn"
  get "/faq", to: "home#faq"
  get "/first_last_best_worst", to: "home#first_last_best_worst"
  get "/homework_for_life", to: "home#homework_for_life"
  get "/ai_feedback", to: "home#ai_feedback"
  get "/pricing", to: "home#pricing"
  get "/privacy", to: "home#privacy"
  get "/terms", to: "home#terms"

  get "/find_stories", to: "home#find_stories"
  get "/craft_stories", to: "home#craft_stories"
  get "/share_stories", to: "home#share_stories"

  # Audience-specific landing pages
  get "/storycoach_for_entrepreneurs", to: "home#storycoach_for_entrepreneurs"
  get "/storycoach_for_aspiring_writers", to: "home#storycoach_for_aspiring_writers"
  get "/storycoach_for_executives", to: "home#storycoach_for_executives"

  authenticated :user, lambda { |u| u.admin? } do
    mount Sidekiq::Web => '/sidekiq'
  end

  resources :announcements, only: [:index]
  devise_for :users, controllers: {
    registrations: "users/registrations",
    sessions: "users/sessions",
    omniauth_callbacks: "users/omniauth_callbacks"
  }

  namespace :api do
    namespace :v1 do
      resources :users, only: [:show, :update]
      resources :exercises, only: [:index, :show, :create]
      get "quote_of_the_day", to: "resources#quote_of_the_day"
    end
  end

  root to: "home#index"

  resources :charges, only: [:new, :create]

  resources :exercises do
    collection do
      get :search
    end
  end
  resources :stories do
    member do
      patch :publish
      patch :unpublish
    end
  end

  get "/users/:user_id/stories", to: "stories#public_stories", as: "public_user_stories"

  resources :memory_bank_exercises, only: [:new, :create]
  resources :random_word_exercises, only: [:new, :create]
  resources :copy_work_exercises, only: [:new, :create]
  resources :memory_inspiration_exercises, only: [:new, :create]

  resources :linked_in_stories, only: [:new, :create, :edit, :update]
  resources :introduction_stories, only: [:new, :create, :edit, :update]
  resources :origin_stories, only: [:new, :create, :edit, :update]

  resources :users, only: [:show, :update] do
    member do
      patch :update_to_premium
      get "get-started", to: "users#get_started"
    end
  end

  post "/feedback", to: "story_feedback#openai_feedback"

  resources :postmark_inbounds, only: [:create]
end
