# After 1 blocked requests in 10 minutes, block all requests from that IP for 1 day.
Rack::Attack.blocklist('fail2ban pentesters') do |req|
  # `filter` returns truthy value if request fails, or if it's from a previously banned IP so the request is blocked
  Rack::Attack::Fail2Ban.filter(
    "pentesters-#{req.ip}",
    maxretry: 1,
    findtime: 10.minutes,
    bantime: 1.day
  ) do
    # The count for the IP is incremented if the return value is truthy
    req.path.include?('wp-admin') ||
    req.path.include?('wp-login') ||
    req.path.include?('wp-includes')
  end
end

class Rack::Attack
  throttle('limit signups per IP', limit: 5, period: 60.seconds) do |req|
    req.ip if req.path == '/users' && req.post?
  end
end