# Do yourself a favor and set these up right when you install the engine.

CanonicalRails.setup do |config|
  # Force the protocol. If you do not specify, the protocol will be based on the incoming request's protocol.
  config.protocol = "https://"
  # This is the main host, not just the TLD, omit slashes and protocol. If you have more than one, pick the one you want to rank in search results.
  config.host = "storycoach.app"
  config.collection_actions = []
  # We often allow page as a parameter because we use pagination and want each page considered
  config.allowed_parameters = %i[page]
  # We already set this in the application view, but if you want this gem to generate an OpenGraph URL for you, set this to true
  config.opengraph_url = false
end
