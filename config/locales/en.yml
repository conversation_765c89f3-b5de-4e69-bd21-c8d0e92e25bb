# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   "true": "foo"
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  user_mailer:
    welcome:
      subject: "Welcome to StoryCoach - Unleash Your Storytelling Potential!"
    first_badge:
      subject: "✨✨✨ Congratulations! You earned a badge"
    first_exercise_badge:
      subject: "✨✨✨ Congratulations! You earned a new badge for completing your first exercise"
    first_story_badge:
      subject: "✨✨✨ Congratulations! You earned a new badge for drafting your first story"
    next_level:
      subject: "✨✨✨ New Level Reached!"
  admin_mailer:
    new_user:
      subject: "StoryCoach.app has a new user"
