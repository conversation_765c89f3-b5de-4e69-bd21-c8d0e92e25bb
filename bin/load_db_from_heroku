#!/usr/bin/env ruby
require "pathname"

# path to your application root.
APP_ROOT = Pathname.new File.expand_path("../../", __FILE__)
SKIP_CAPTURE = ARGV[0]

Dir.chdir APP_ROOT do
  unless SKIP_CAPTURE
    puts "== Capture latest backup =="
    system "heroku pg:backups:capture --app storycoach-prod"

    puts "== Download latest backup =="
    system "rm latest.*"
    system "heroku pg:backups:download --app storycoach-prod"

    puts "== Restore the backup to local db =="
  end

  if File.exist?("latest.dump")
    system "bundle exec rails db:drop"
    system "bundle exec rails db:create"
    system "pg_restore --verbose --clean --no-acl --no-owner -h localhost  -d storyworthy_development latest.dump"
  end

  puts "== Migrate =="
  system "bundle exec rails db:migrate"

  puts "== Clear any background jobs"
  system "redis-cli flushall"
end
