GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    anthropic (0.3.2)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    aws-eventstream (1.3.0)
    aws-partitions (1.941.0)
    aws-sdk-core (3.197.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.83.0)
      aws-sdk-core (~> 3, >= 3.197.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.152.0)
      aws-sdk-core (~> 3, >= 3.197.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.8)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    bootstrap-email (1.5.1)
      htmlbeautifier (~> 1.3)
      nokogiri (~> 1.6)
      premailer (~> 1.7)
      sass-embedded (~> 1.53)
    builder (3.3.0)
    canonical-rails (0.2.16)
      actionview (>= 4.1, < 7.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    case_transform (0.2)
      activesupport
    childprocess (5.0.0)
    chronic (0.10.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.17.1)
      addressable
    cssbundling-rails (1.4.0)
      railties (>= 6.0.0)
    date (3.4.1)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    dotenv (2.8.1)
    drb (2.2.1)
    erubi (1.13.1)
    event_stream_parser (1.0.0)
    execjs (2.10.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.4.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.1-aarch64-linux-gnu)
    ffi (1.17.1-aarch64-linux-musl)
    ffi (1.17.1-arm-linux-gnu)
    ffi (1.17.1-arm-linux-musl)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86-linux-gnu)
    ffi (1.17.1-x86-linux-musl)
    ffi (1.17.1-x86_64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    ffi (1.17.1-x86_64-linux-musl)
    fiber-storage (1.0.0)
    foreman (0.88.1)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gon (6.4.0)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    google-protobuf (4.27.1)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.1-aarch64-linux)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.1-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.1-x86-linux)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.1-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.27.1-x86_64-linux)
      bigdecimal
      rake (>= 13)
    graphlient (0.8.0)
      faraday (~> 2.0)
      graphql-client
    graphql (2.4.10)
      base64
      fiber-storage
      logger
    graphql-client (0.25.0)
      activesupport (>= 3.0)
      graphql (>= 1.13.0)
    hashdiff (1.1.0)
    hashie (5.0.0)
    htmlbeautifier (1.4.3)
    htmlentities (4.3.4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jsbundling-rails (1.3.0)
      railties (>= 6.0.0)
    json (2.10.1)
    jsonapi-renderer (0.2.2)
    jwt (2.8.1)
      base64
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    logger (1.6.6)
    lograge (0.13.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    madmin (1.2.11)
      pagy (>= 3.5)
      rails (>= 6.0.3)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    meta-tags (2.22.0)
      actionpack (>= 6.0.0, < 8.1)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.4)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.5)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.2)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.2-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.2-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.1.2)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    orm_adapter (0.5.0)
    pagy (6.5.0)
    pg (1.5.6)
    postmark (1.25.0)
      json
    postmark-rails (0.22.1)
      actionmailer (>= 3.0.0)
      postmark (>= 1.21.3, < 2.0)
    pp (0.6.2)
      prettyprint
    premailer (1.23.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    pretender (0.3.4)
      actionpack (>= 4.2)
    prettyprint (0.2.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (5.6.8)
      nio4r (~> 2.0)
    pundit (2.3.2)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.11.0)
      psych (>= 4.0.0)
    recaptcha (5.17.0)
    redis (4.8.1)
    regexp_parser (2.9.2)
    reline (0.6.0)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.9)
      strscan
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    ruby-openai (7.0.1)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    rubyzip (2.3.2)
    sass-embedded (1.77.4-aarch64-linux-gnu)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-aarch64-linux-musl)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-arm-linux-gnueabihf)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-arm-linux-musleabihf)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-x86-linux-gnu)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-x86-linux-musl)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-x86_64-darwin)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-x86_64-linux-gnu)
      google-protobuf (>= 3.25, < 5.0)
    sass-embedded (1.77.4-x86_64-linux-musl)
      google-protobuf (>= 3.25, < 5.0)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.3)
      railties (>= 6.0.0)
    stringio (3.1.2)
    stripe (8.7.0)
    strscan (3.1.0)
    terser (1.2.5)
      execjs (>= 0.3.0, < 3)
    thor (1.3.2)
    tilt (2.6.0)
    timeout (0.4.3)
    turbo-rails (2.0.5)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (1.0.3)
    useragent (0.16.11)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    weaviate-ruby (0.9.2)
      faraday (>= 2.0.1, < 3.0)
      graphlient (>= 0.7.0, < 0.9.0)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.10)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.1)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux
  arm-linux-gnu
  arm-linux-gnueabihf
  arm-linux-musl
  arm-linux-musleabihf
  arm64-darwin
  x86-linux
  x86-linux-gnu
  x86-linux-musl
  x86_64-darwin
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  active_model_serializers (~> 0.10.15)
  anthropic
  aws-sdk-s3
  bootsnap
  bootstrap-email
  canonical-rails (~> 0.2.15)
  capybara
  cssbundling-rails
  debug
  devise (~> 4.9, >= 4.9.4)
  dotenv (~> 2.8, >= 2.8.1)
  factory_bot_rails (>= 6.2)
  faker
  foreman (~> 0.88.1)
  friendly_id (~> 5.5.0)
  gon
  importmap-rails
  jbuilder
  jsbundling-rails
  letter_opener
  lograge (~> 0.13.0)
  madmin (~> 1.2, >= 1.2.10)
  meta-tags
  name_of_person (~> 1.1)
  omniauth-google-oauth2 (~> 1.1, >= 1.1.1)
  omniauth-rails_csrf_protection (~> 1.0, >= 1.0.1)
  pagy (~> 6.3)
  pg (~> 1.1)
  postmark-rails
  pretender (~> 0.3.4)
  puma (~> 5.0)
  pundit (~> 2.1)
  rack-attack
  rails (~> 7.2.2)
  recaptcha (~> 5.17)
  redis (~> 4.0)
  responders (~> 3.1)
  rspec-rails
  ruby-openai
  sassc-rails
  selenium-webdriver
  shoulda-matchers (~> 4.0)
  sidekiq (~> 6.2)
  sitemap_generator (~> 6.1)
  sprockets-rails
  stimulus-rails
  stripe (~> 8.0)
  terser
  turbo-rails
  tzinfo-data
  weaviate-ruby (~> 0.9.2)
  web-console
  webdrivers
  webmock (~> 3.18, >= 3.18.1)
  whenever

RUBY VERSION
   ruby 3.3.8p144

BUNDLED WITH
   2.5.11
