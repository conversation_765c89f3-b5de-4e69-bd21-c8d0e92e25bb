# Use the official Ruby 3.3.8 image
ARG RUBY_VERSION=3.3.8
FROM ruby:$RUBY_VERSION

# Install essential build tools, Node.js, Yarn, and PostgreSQL client
ARG NODE_MAJOR=20
RUN apt-get update -qq \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        gnupg \
        libpq-dev \
        postgresql-client \
    # Add Node.js repository and install Node.js and Yarn
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
    && apt-get update -qq \
    && apt-get install -y nodejs \
    && npm install -g yarn \
    # Clean up APT caches
    && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /rails

# Install Bundler
RUN gem install bundler

# Copy Gemfile and Gemfile.lock
COPY Gemfile Gemfile.lock ./

# Install gems
RUN bundle install

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install Node packages
RUN yarn install --check-files

# Copy the rest of the application code
COPY . .

# Copy the entrypoint script
COPY docker-entrypoint.sh /usr/bin/
RUN chmod +x /usr/bin/docker-entrypoint.sh

# Set the entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]

# Expose port 3000
EXPOSE 3000

# Default command to start the Rails server
CMD ["bin/rails", "server", "-b", "0.0.0.0"]
