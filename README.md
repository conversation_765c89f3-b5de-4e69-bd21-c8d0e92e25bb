# StoryCoach

### Ruby and Rails versions

* `ruby-3.3.8`
* `Rails 7.x.x`
* This app uses `dotenv` for environment variables

## Running the Application

You can run StoryCoach either locally on your machine or using Docker. Choose the method that works best for your development environment.

### Option 1: Running with <PERSON><PERSON> or Orb<PERSON>tack

#### Prerequisites
- [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/install/)

  OR

- [OrbStack](https://orbstack.dev/) (Mac only) - A faster, more resource-efficient alternative to Docker Desktop

#### Setup

1. Clone the repository:
   ```
   <NAME_EMAIL>:stockandawe/storycoach.git
   cd storycoach
   ```

2. Create environment file:
   ```
   cp .env-example .env
   ```

3. Edit the `.env` file to set required environment variables, including:
   - `DB_PASSWORD` (used by docker-compose.yml)
   - `REDIS_URL` (if not using the default)
   - `STRIPE_PUBLISHABLE_KEY` and `STRIPE_SECRET_KEY`
   - Any other required environment variables

4. Build and start the Docker containers:
   ```
   docker-compose build
   docker-compose up
   ```

5. In a separate terminal, set up the database:
   ```
   docker-compose exec web bundle exec rails db:create
   docker-compose exec web bundle exec rails db:migrate
   ```

6. Load seed database
   ```
   docker-compose exec web bundle exec rails db:seed
   ```
7. [Optional] Weaviate vector search
   1. Login to [Weaviate Cloud](https://console.weaviate.cloud/) and create a `storycoach-dev` cluster
   2. Copy the `REST Endpoint` and set it as `WEAVIATE_URL` in `.env`
   3. Copy the Admin API Key and set it as `WEAVIATE_API_KEY` in `.env`
   4. Run the `run_weaviate_initialization_services` script
   ```
   docker-compose exec web bundle exec rake run_weaviate_initialization_services
   ```

#### Common Docker Commands

- Start the application:
  ```
  docker-compose up
  ```

- Start the application in detached mode (background):
  ```
  docker-compose up -d
  ```

- Stop the application:
  ```
  docker-compose down
  ```

- View logs:
  ```
  docker-compose logs -f
  ```

- Run Rails commands:
  ```
  docker-compose exec web bundle exec rails [command]
  ```

- Run database migrations:
  ```
  docker-compose exec web bundle exec rails db:migrate
  ```

- Access Rails console:
  ```
  docker-compose exec web bundle exec rails console
  ```

- Run rspec tests:
  ```
  docker-compose exec web bundle exec rspec
  ```

### Option 2: Running Locally

#### Prerequisites
- Ruby 3.3.8
- Node.js and Yarn
- PostgreSQL
- Redis

#### Getting started

1. Clone the repository:
   ```
   <NAME_EMAIL>:stockandawe/storycoach.git
   cd storycoach
   ```

2. Setup:
   ```
   bundle install
   yarn install
   cp .env-example .env
   ```

3. Configure your `.env` file with appropriate values for:
   - Database connection
   - Redis connection
   - Stripe keys
   - Other required environment variables

4. Set up the database:
   ```
   bundle exec rails db:create
   bundle exec rails db:migrate
   ```

5. Run the app:
   ```
   foreman start
   ```

## Development Environment Comparison

| Feature | Docker | OrbStack | Local |
|---------|--------|----------|-------|
| **Setup Complexity** | Simpler initial setup, consistent environment | Simpler initial setup, consistent environment | Requires installing dependencies manually |
| **Performance** | Slower due to containerization | Near-native performance on Mac | Native performance |
| **Database** | PostgreSQL container included | PostgreSQL container included | Requires local PostgreSQL installation |
| **Redis** | Redis container included | Redis container included | Requires local Redis installation |
| **Environment** | Isolated from system | Isolated from system | Uses system-installed dependencies |
| **File Changes** | Auto-detected via volume mounting | Auto-detected via volume mounting | Auto-detected directly |
| **Debugging** | Requires connecting to container | Requires connecting to container | Direct access |
| **Resource Usage** | Higher (Docker Desktop can be resource-intensive) | Lower (more efficient than Docker Desktop) | Lower (runs processes directly) |
| **Platform** | Cross-platform | Mac only | Cross-platform |

### OrbStack Benefits (Mac Only)

If you're developing on a Mac, [OrbStack](https://orbstack.dev/) offers several advantages over Docker Desktop:

- Significantly faster container startup times
- Lower CPU and memory usage
- Better battery life on MacBooks
- Native filesystem performance
- Seamless integration with macOS
- Built-in SSH client for containers

OrbStack is fully compatible with Docker Compose, so you can use the same docker-compose.yml file and commands. Simply install OrbStack instead of Docker Desktop, and use the same docker-compose commands as listed above.

## External Services Configuration

### Postmark & Email Delivery + Inbound

#### For Email Delivery:
* Save your Postmark Server API Token to `config/credentials.yml.enc`:
  * For local development:
    ```
    EDITOR="vim" bin/rails credentials:edit
    ```
  * For Docker:
    ```
    docker-compose exec web bin/rails credentials:edit
    ```

#### For Inbound Email:
* For local development:
  ```
  gem install ultrahook
  ultrahook inbound-demo 3000
  ```
* For Docker:
  ```
  gem install ultrahook
  ultrahook inbound-demo 3000
  ```
  Note: When using Docker, the ultrahook command runs on your host machine, not in the container, and forwards requests to port 3000 which is mapped to the container.

### Stripe Configuration

* Set the following variables in your `.env` file:
  ```
  STRIPE_PUBLISHABLE_KEY=your_publishable_key
  STRIPE_SECRET_KEY=your_secret_key
  ORDER_AMOUNT=amount_in_cents
  ```
* These environment variables will be automatically loaded in both local and Docker environments.

## Troubleshooting Docker Setup

### Common Issues and Solutions

1. **Database Connection Issues**
   * **Problem**: Web container can't connect to the database
   * **Solution**: Ensure the `DB_PASSWORD` in your `.env` file matches the `POSTGRES_PASSWORD` in docker-compose.yml
   * **Check**: Run `docker-compose logs db` to see if PostgreSQL started correctly

2. **Port Conflicts**
   * **Problem**: "Port already in use" errors
   * **Solution**: Change the port mapping in docker-compose.yml (e.g., "3001:3000" instead of "3000:3000")
   * **Check**: Run `lsof -i :3000` to see what's using port 3000

3. **Container Startup Failures**
   * **Problem**: Web container exits immediately
   * **Solution**: Check logs with `docker-compose logs web`
   * **Fix**: Ensure all required environment variables are set in your `.env` file

4. **Volume Permissions**
   * **Problem**: Permission denied errors when accessing mounted volumes
   * **Solution**: Fix permissions on the host directory or use Docker user mapping
   * **Check**: Run `ls -la` to verify file ownership and permissions

5. **Slow Performance**
   * **Problem**: Docker development environment feels sluggish
   * **Solution**: Increase Docker resource allocation in Docker Desktop settings
   * **Alternative**: Consider using OrbStack (Mac only) for near-native performance or switch to local development

### Resetting the Docker Environment

If you need a fresh start:

```
# Stop all containers
docker-compose down

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Rebuild containers
docker-compose build

# Start fresh
docker-compose up
```

### OrbStack-Specific Troubleshooting

If you're using OrbStack instead of Docker Desktop, here are some specific troubleshooting tips:

1. **OrbStack UI**
   * OrbStack provides a user-friendly UI that shows all running containers, logs, and resource usage
   * Access it from the OrbStack menu bar icon to quickly diagnose issues

2. **Performance Optimization**
   * OrbStack is already optimized for performance, but you can further improve it by:
     * Using the built-in file sharing instead of Docker volumes when possible
     * Keeping your project on the main drive (not network drives)

3. **Container Management**
   * Use the OrbStack UI to easily restart, stop, or delete containers
   * You can also access container shells directly from the UI

4. **Logs and Debugging**
   * OrbStack provides easy access to logs through its UI
   * You can also use `docker-compose logs` as you would with Docker Desktop

5. **Updating OrbStack**
   * Keep OrbStack updated to the latest version for best performance and compatibility
   * Updates can be installed from the OrbStack menu
