# Git files
.git
.gitignore
.gitattributes

# Docker files
.dockerignore
Dockerfile
docker-compose.yml

# Logs and temporary files
log/*
tmp/*
!log/.keep
!tmp/.keep

# Storage files
storage/*
!storage/.keep

# Node modules
node_modules

# Yarn files
.yarn/cache
.yarn/install-state.gz
.yarn/patches
.yarn/plugins
.yarn/releases
.yarn/sdks
.yarn/versions
.yarnrc.yml
yarn-error.log

# Build artifacts
public/assets
public/vite
app/assets/builds/*
!app/assets/builds/.keep

# Credentials
config/master.key
config/credentials.yml.enc

# Local environment files
.env*

# OS generated files
.DS_Store
*~
*.swp
