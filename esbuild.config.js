const path = require('path')
const rails = require('esbuild-rails')
const { sassPlugin } = require('esbuild-sass-plugin')

const watch = process.argv.includes("--watch") && {
  onRebuild(error) {
    if (error) console.error("[watch] build failed", error);
    else console.log("[watch] build finished");
  },
};

require("esbuild").build({
  entryPoints: ["application.jsx"],
  bundle: true,
  outdir: path.join(process.cwd(), "app/assets/builds"),
  absWorkingDir: path.join(process.cwd(), "app/javascript"),
  watch: watch,
  plugins: [
    rails(),
    sassPlugin({
      loadPaths: [path.join(process.cwd(), 'node_modules')]
    })
  ],
  minify: true,
  sourcemap: false,
}).catch(() => process.exit(1));
