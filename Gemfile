source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.3.8"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.2.2"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"

# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"

# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"

# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Use Redis adapter to run Action Cable in production
gem "redis", "~> 4.0"

# Cleaner logging
gem 'lograge', '~> 0.13.0'

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Sass to process CSS
gem "sassc-rails"

# Use Terser to minify JavaScript
gem "terser"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw x64_mingw ]
  gem "dotenv", "~> 2.8", ">= 2.8.1", groups: [:development, :test]
  gem "rspec-rails"
  gem "factory_bot_rails", ">= 6.2"
  gem "webmock", "~> 3.18", ">= 3.18.1"
  gem "shoulda-matchers", "~> 4.0"
end

gem "faker"

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"
  gem 'foreman', '~> 0.88.1'

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver"
  gem "webdrivers"
end

gem "cssbundling-rails"
gem "devise", "~> 4.9", ">= 4.9.4"
gem "friendly_id", "~> 5.5.0"
gem "jsbundling-rails"
gem "madmin", "~> 1.2", ">= 1.2.10"
gem "name_of_person", "~> 1.1"
gem "omniauth-google-oauth2", "~> 1.1", ">= 1.1.1"
gem "omniauth-rails_csrf_protection", "~> 1.0", ">= 1.0.1"
gem "pretender", "~> 0.3.4"
gem "pundit", "~> 2.1"
gem "sidekiq", "~> 6.2"
gem "sitemap_generator", "~> 6.1"
gem "whenever", require: false
gem "responders", "~> 3.1"

# payment
gem "stripe", "~> 8.0"

# Email
gem "letter_opener", group: :development
gem "bootstrap-email"
gem "postmark-rails"

# openai
gem "ruby-openai"
gem "anthropic"

# SEO tools: https://tosbourn.com/ruby-on-rails-seo/
gem "meta-tags"
gem "canonical-rails", "~> 0.2.15" # Canonical Tag management

# Prevent wp-admin scanning, etc
# Rack middleware for blocking & throttling abusive requests
gem "rack-attack"

gem "aws-sdk-s3", require: false

gem "pagy", "~> 6.3"

gem "gon"

gem "recaptcha", "~> 5.17"

gem "weaviate-ruby", "~> 0.9.2"

gem "active_model_serializers", "~> 0.10.15"
