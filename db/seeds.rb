require "csv"

# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

u1 = User.create(
  first_name: "<PERSON><PERSON><PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  password: "supersec",
  admin: true
)

u1.exercises.create(
  category: Exercise.categories[:homework_for_life],
  note: "I realized that creating something takes a lot of work",
  note_date: DateTime.now,
)

DailyPrompt.create(
  question: "Did someone say something today that made you laugh? Cry? Become angry? Feel confused?"
)

Resource.create(
  content: "The shortest distance between a human being and the truth is a story."
)

Resource.create(
  content: "A good storyteller has the power to captivate hearts, awaken minds, and inspire change."
)

u2 = User.create(
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  password: "supersec"
)

# Read memories from CSV file and create exercises
CSV.foreach(Rails.root.join("db", "memories.csv"), headers: true) do |row|
  u2.exercises.create(
    category: Exercise.categories[:homework_for_life],
    note: row["Memory"],
    note_date: DateTime.parse(row["Date"])
  )
end