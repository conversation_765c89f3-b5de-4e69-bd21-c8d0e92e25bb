class ApplicationController < ActionController::Base
  impersonates :user
  include Pundit::Authorization

  # Disable CSRF for JSON API requests
  protect_from_forgery with: :exception, unless: -> { request.format.json? }

  # Authenticate API user if the request format is JSON
  before_action :authenticate_api_user, if: -> { request.format.json? }
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_gon_user

  attr_reader :current_api_user

  # This is used by <PERSON><PERSON> to access the current authenticated user in API requests.
  # Here we override it to use `current_api_user` instead.
  def current_user
    super || @current_api_user
  end

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:name, :first_name, :last_name])
    devise_parameter_sanitizer.permit(:account_update, keys: [:name, :first_name, :last_name, :avatar])
  end

  private

  # Authenticate the API user based on API key sent in the Authorization header
  def authenticate_api_user
    api_key = request.headers['Authorization']

    # Search for user by API key (assumed to be stored in `api_key` column on the User model)
    @current_api_user = User.find_by(api_key: api_key)

    unless @current_api_user
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  # Set the `gon` user for front-end if needed (this is useful for client-side JS)
  def set_gon_user
    if user_signed_in?
      gon.current_user = {
        id: current_user.id,
        name: current_user.name,
        email: current_user.email
      }
    end
  end
end
