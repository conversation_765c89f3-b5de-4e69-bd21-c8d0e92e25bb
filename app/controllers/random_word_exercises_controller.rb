class RandomWordExercisesController < ApplicationController
  before_action :authenticate_user!

  def new
      @user=current_user
      @exercise = @user.exercises.new(category: "random_word")
      @random_words = [
        Faker::Sport.sport.downcase,
        Faker::Creature::Animal.name.downcase,
        Faker::Color.color_name.downcase
      ]
  end

  def create
    @user=current_user

    # Create a new exercise based on the form parameters
    @exercise = @user.exercises.new(exercise_params)
    @exercise.update(category: "random_word", note_date: Time.zone.now)

    if @exercise.save
      flash[:notice] = "Successfully completed a Random Word exercise."
      redirect_to exercises_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  private

  def exercise_params
    params.require(:exercise).permit(:note)
  end
end
