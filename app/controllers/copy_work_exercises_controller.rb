class CopyWorkExercisesController < ApplicationController
  before_action :authenticate_user!

  def new
    @user=current_user
    @exercise = @user.exercises.new(category: "copy_work")
    @copy_inspiration = copy_inspiration
  end

  def create
    @user=current_user

    # Create a new exercise based on the form parameters
    @exercise = @user.exercises.new(exercise_params)
    @exercise.update(category: "copy_work", note_date: Time.zone.now)

    if @exercise.save
      flash[:notice] = "Successfully completed a Copy Work exercise."
      redirect_to root_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  private

  def copy_inspiration
    file_path = Rails.root.join("db", "writing_music_by_gary_provost.txt")

    if File.exist?(file_path)
      file = File.open(file_path)
      content = file.read
      return content
    else
      Rails.logger.error "File #{file_path} not found."
    end
  end

  def exercise_params
    params.require(:exercise).permit(:note)
  end
end
