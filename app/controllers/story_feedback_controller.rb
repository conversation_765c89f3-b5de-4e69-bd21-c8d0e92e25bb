class StoryFeedbackController < ApplicationController
  before_action :authenticate_user!

  def openai_feedback
    story_id = params[:story_id]

    if story_id.nil?
      render :stories_path
      return
    end

    story = current_user.stories.find(story_id)

    ai_feedback_credits_available = current_user.ai_feedback_credits

    if (ai_feedback_credits_available < 1)
      flash[:error] = "No credits available to get AI feedback"
      redirect_to edit_story_path(story)
      return
    end

    case ENV["STORY_FEEDBACK_AI_MODEL"]
    when "openai"
      service = OpenaiStoryFeedbackService.new
    when "anthropic"
      service = AnthropicStoryFeedbackService.new
    else
      service = OpenaiStoryFeedbackService.new
    end

    feedback = service.call(story.content)

    unless feedback.nil?
      current_user.update(ai_feedback_credits: ai_feedback_credits_available - 3)
      stories_path
      story.feedbacks.create(content: feedback)
    end

    redirect_to edit_story_path(story)
  end
end