class Api::V1::ExercisesController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_api_user
  before_action :set_exercise, only: [:show]

  # GET /api/v1/exercises
  def index
    exercises = @current_api_user.exercises.order(note_date: :desc)

    render json: exercises, each_serializer: ExerciseSerializer, status: :ok
  end

  # GET /api/v1/exercises/:id
  def show
    render json: @exercise, serializer: ExerciseSerializer, status: :ok
  end

  # POST /api/v1/exercises
  def create
    exercise = @current_api_user.exercises.build(exercise_params)

    if exercise.save
      render json: exercise, status: :created
    else
      render json: { errors: exercise.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def set_exercise
    @exercise = @current_api_user.exercises.find_by(id: params[:id])
    render json: { error: 'Exercise not found' }, status: :not_found unless @exercise
  end

  def exercise_params
    params.require(:exercise).permit(:note, :note_date, :category, :photo)
  end
end
