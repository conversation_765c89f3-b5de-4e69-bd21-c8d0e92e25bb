class Api::V1::UsersController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_api_user
  before_action :set_user, only: [:show, :update]

  def show
    render json: @user, serializer: UserSerializer, status: :ok
  end

  def update
    if @user.update(user_params)
      render json: @user, serializer: UserSerializer, status: :ok
    else
      render json: { errors: @user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:practice_frequency, :practice_time_of_day)
  end

  def set_user
    @user = User.find_by(api_key: request.headers['Authorization'])
  end
end