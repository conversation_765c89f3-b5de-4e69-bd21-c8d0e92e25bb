class MemoryInspirationExercisesController < ApplicationController
  before_action :authenticate_user!

  def new
    @user=current_user
    @exercise = @user.exercises.new(category: "memory_inspiration")
    @available_prompts = Exercise::MEMORY_INSPIRATION_PROMPT
  end

  def create
    @user=current_user

    # Create a new exercise based on the form parameters
    @exercise = @user.exercises.new(exercise_params)
    @exercise.update(category: "memory_inspiration", note_date: Time.zone.now)

    if @exercise.save
      flash[:notice] = "Successfully saved a storyworthy memory."
      redirect_to exercises_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  private

  def exercise_params
    params.require(:exercise).permit(:note)
  end
end
