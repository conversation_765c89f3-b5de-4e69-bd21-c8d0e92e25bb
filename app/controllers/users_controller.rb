class UsersController < ApplicationController
  include Pagy::Backend
  before_action :authenticate_user!

  PREMIUM_SUBSCRIBER_AI_CREDITS = 25

  def show
    initialize_attributes

    @user = User.friendly.find(params[:id])

    @practice_frequency = @user.practice_frequency
    @exercise = @user.exercises.new(category: "homework_for_life")
    @random_user_memory = current_user.exercises.homework_for_life.sample
    @latest_memory = current_user.exercises.homework_for_life.order(created_at: :desc).first
    @public_stories = Story.where(published: true).where.not(user: self).order(created_at: :desc).limit(5)

    @pagy, @stories = pagy(current_user.stories.order(created_at: :desc))

    @page_title = "Dashboard"
    
    # Transfer the notice to the view for animation purposes if it's a memory saved message
    if flash[:notice]&.include?("Successfully saved a storyworthy memory")
      flash[:memory_saved] = true
    end
  end

  def update
    @user = current_user

    if @user.update(user_params)
      flash[:notice] = "Successfully updated!"
      redirect_to user_path(current_user)
    end
  end

  def update_to_premium
    @user = current_user

    @user.update(premium_subscriber: true)
    @user.update(ai_feedback_credits: @user.ai_feedback_credits + PREMIUM_SUBSCRIBER_AI_CREDITS)

    redirect_to @user, notice: "You are signed up for Premium!"
  end

  def get_started
    @user = current_user
  end

  private

  def initialize_attributes
    @badges = current_user.badges
    @badge = current_user.badges.last
    @points_to_level_up =  points_to_next_level(current_user)
    @ai_feedback_credits = current_user.ai_feedback_credits
    @levels_and_points_map = User::LEVEL_POINTS_AND_CREDITS_MAPPING
    @inspiration_for_the_day = Resource.all.sample
  end

  def points_to_next_level(user)
    next_level = user.level + 1
    User::LEVEL_POINTS_AND_CREDITS_MAPPING[next_level][:points] - user.points
  end

  def user_params
    params.require(:user).permit(:goal, :practice_frequency, :practice_time_of_day)
  end
end