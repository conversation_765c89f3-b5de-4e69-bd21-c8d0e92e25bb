class OriginStoriesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_placeholder, only: [:new, :edit]
  before_action :set_example, only: [:new, :edit]

  def new
    @user=current_user
    @story = @user.stories.new(category: "origin")
  end

  def create
    @user=current_user

    # Create a new story based on the form parameters
    @story = @user.stories.new(story_params)
    @story.update(category: "origin")

    if @story.save
      flash[:notice] = "Successfully saved your origin story"
      redirect_to stories_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  def edit
    @story = current_user.stories.friendly.find(params[:id])
    @ai_feedback_credits = current_user.ai_feedback_credits
    @latest_feedback = @story.feedbacks.last
  end

  def update
    @story = current_user.stories.find(params[:id])

    if @story.update(story_params)
      flash[:notice] = 'Story was successfully updated.'
      redirect_to stories_path
    else
      render "edit"
    end
  end

  private

  def story_params
    params.require(:story).permit(:content, :title)
  end

  def set_placeholder
    @origin_story_placeholder = "I'd heard that Christmas trees sold for a pound a foot high. And so I thought, \
well, if I plant the Christmas trees and I wait two or three years, they will all grow, and \
our 5 pounds will turn into 1,000 pounds. So on the Easter holidays, I planted the seedlings. \
I went off to boarding school for the term, and I dreamed of what would grow. Anyway, the rabbits \
got in and ate all the trees. I remember walking up to the field when I'd come home from school \
one day and just seeing a wasteland where every single Christmas tree had been eaten.

At that moment, I realized a well-thought-out scheme can go south when you're not closely tending to \
your investment. And I think, throughout my life, if something's gone wrong and there's nothing we \
can do about it, accept it gracefully. Put it behind you. Pick yourself up and start again.

-Sir Richard Branson"
  end

  def set_example
    @origin_story_example = "I'd heard that Christmas trees sold for a pound a foot high. And so I thought, \
      well, if I plant the Christmas trees and I wait two or three years, they will all grow, and \
      our 5 pounds will turn into 1,000 pounds. So on the Easter holidays, I planted the seedlings. \
      I went off to boarding school for the term, and I dreamed of what would grow. Anyway, the rabbits \
      got in and ate all the trees. I remember walking up to the field when I'd come home from school \
      one day and just seeing a wasteland where every single Christmas tree had been eaten.

      At that moment, I realized a well-thought-out scheme can go south when you're not closely tending to \
      your investment. And I think, throughout my life, if something's gone wrong and there's nothing we \
      can do about it, accept it gracefully. Put it behind you. Pick yourself up and start again.

      -Sir Richard Branson"
  end
end
