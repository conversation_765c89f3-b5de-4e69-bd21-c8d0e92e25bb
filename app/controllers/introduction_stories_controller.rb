class IntroductionStoriesController < ApplicationController
  before_action :authenticate_user!

  def new
      @user=current_user
      @story = @user.stories.new(category: "introduction")
      @placeholder = "I am #{Rails.configuration.application_name}. I help anyone level up their storytelling skills " +
      "using AI-powered writing tools, developing daily habits, and with skill-building exercises. " +
      "Anyone can become a better storyteller by remembering the storyworthy moments from their day-to-day lives " +
      "and practicing and refining how they share those moments with others."
  end

  def create
    @user=current_user

    # Create a new story based on the form parameters
    @story = @user.stories.new(story_params)
    @story.update(category: "introduction")

    if @story.save
      flash[:notice] = "Successfully saved your introduction story"
      redirect_to stories_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  def edit
    @story = current_user.stories.friendly.find(params[:id])
    @ai_feedback_credits = current_user.ai_feedback_credits
    @latest_feedback = @story.feedbacks.last
  end

  def update
    @story = current_user.stories.find(params[:id])

    if @story.update(story_params)
      flash[:notice] = 'Story was successfully updated.'
      redirect_to stories_path
    else
      render "edit"
    end
  end

  private

  def story_params
    params.require(:story).permit(:content, :title)
  end
end
