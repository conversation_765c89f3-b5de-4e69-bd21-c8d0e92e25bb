class PostmarkInboundsController < ApplicationController
    protect_from_forgery except: :create

    def create
        message = JSON.parse(request.body.read)

        sender_email = message["From"]

        if sender_email.nil?
            Rails.logger.error "Error parsing sender_email for incoming message #{message}"
            render plain: "Bad message", status: 400
        else
            Rails.logger.info "Recevied email from #{sender_email}"
        end

        notification_date = message["Subject"].gsub("Re: StoryCoach's reminder to save a memory from the day - ", "")

        unless valid_date?(notification_date)
            Rails.logger.error "Unable to parse date from subject line: #{message["Subject"]}"
            notification_date = Time.now.strftime("%m/%d/%Y")
        end

        Rails.logger.info "Notification date is #{notification_date}"

        subscriber = User.find_by_email sender_email

        if subscriber.nil?
            Rails.logger.error "Unable to find user with email #{sender_email}"
            render plain: "Unable to save", status: 400
            return
        else
            Rails.logger.info "Found user with id #{subscriber.id} and email #{subscriber.email}"
        end

        note = prase_email_response(message["TextBody"])
        subscriber.exercises.create(
            note_date: Date.strptime(notification_date, "%m/%d/%Y").beginning_of_day,
            note: note,
            category: Exercise::categories["homework_for_life"],
        )

        Rails.logger.info "Successfully created H4L exercise for #{subscriber.email}"

        # if message["Attachments"].present?
        #     file_data = message["Attachments"].first["Content"]
        #     filename = message["Attachments"].first["Name"]
        #     # filetype = message["Attachments"].first["ContentType"]
        #     filetype = "image/jpeg"
        #     image_data = Base64.decode64(file_data)
        #     file_io = StringIO.new(file_data)

        #     @exercise.photo.attach(io: file_io, filename: filename)
        #     # @exercise.photo.attach(io: StringIO.new(file_io), filename: filename, content_type: filetype, identify: false)

        #     Rails.logger.info "Successfully attached photo to H4L exercise for #{subscriber.email}"
        # end

        # Return a 200 code so Postmark knows the webhook was processed
        render plain: "Response Saved", status: 200
    end

    private

    def prase_email_response(email_response)
        return EmailResponseParserService.new.call(email_response)
    end

    def valid_date?(notification_date)
        date_format_regex = /\A\d{2}\/\d{2}\/\d{4}\z/

        return false if notification_date.nil?

        return notification_date.match?(date_format_regex)
    end
end