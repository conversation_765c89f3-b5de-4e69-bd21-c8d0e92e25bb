class StoriesController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!, except: [:public_stories, :show]
  before_action :set_story, only: [:edit, :show, :publish, :unpublish]

  def new
      @user = current_user
      @story = @user.stories.new
      @content_value = params["content"]
      @available_prompts = Story::PROMPT
  end

  def create
    @user = current_user

    # Check if we're drafting a story from a memory
    if params[:memory].present? && params[:draft_story] == "true"
      # Check if the user has enough AI credits
      if @user.ai_feedback_credits >= 3
        # Call the AnthropicDraftAStoryService to generate a story and CART analysis
        result = AnthropicDraftAStoryService.new.call(params[:memory])

        if result.present? && result[:story].present?
          # Create a new story with the generated content
          @story = @user.stories.new(content: result[:story])

          if @story.save
            # Deduct 3 AI credits from the user
            @user.update(ai_feedback_credits: @user.ai_feedback_credits - 3)

            # Save the CART analysis as feedback if it's present
            if result[:cart_analysis].present?
              @story.feedbacks.create(content: result[:cart_analysis])
              # Set flag to indicate new feedback was generated
              flash[:new_feedback] = true
            end

            flash[:success] = "Story draft generated successfully! You have #{@user.ai_feedback_credits} AI credits remaining."
            redirect_to edit_story_path(@story)
          else
            flash[:alert] = "Failed to save the story draft. Please try again."
            redirect_to user_path(@user)
          end
        else
          flash[:alert] = "Failed to generate a story draft. Please try again."
          redirect_to user_path(@user)
        end
      else
        flash[:alert] = "You need at least 3 AI credits to generate a story draft."
        redirect_to user_path(@user)
      end
    else
      # Regular story creation from form parameters
      @story = @user.stories.new(story_params)

      if @story.save
        if params[:story] && params[:story][:ai_feedback] == "1"  # "1" means the checkbox was checked
          feedback = AnthropicStoryFeedbackService.new.call(@story.content)

          if feedback.present?
            @user.update(ai_feedback_credits: current_user.ai_feedback_credits - 3)
            @story.feedbacks.create(content: feedback)
            # Set flag to indicate new feedback was generated
            flash[:new_feedback] = true
          end
        end

        flash[:success] = "Well done! You have successfully crafted a new story."
        redirect_to edit_story_path(@story)
      else
        flash[:alert] = "Oops, we have a problem. Let's try again."
        render "new"
      end
    end
  end

  def index
    @stories_drafted = current_user.stories.count

    @page_title = "Write your stories"

    @pagy, @stories = pagy(current_user.stories.order(created_at: :desc))
  end

  def edit
    @ai_feedback_credits = current_user.ai_feedback_credits
    @latest_feedback = @story.feedbacks.last

    # Ensure the feedback content is sanitized for JavaScript display
    if @latest_feedback && @latest_feedback.content
      # Safety measure: sanitize any escaped quotes or characters that might break JS
      @sanitized_feedback_content = @latest_feedback.sanitized_content
    end
  end

  def update
    @story = current_user.stories.friendly.find(params[:id])

    if @story.update(story_params)
      if params[:story][:ai_feedback] == "1"  # "1" means the checkbox was checked
        feedback = AnthropicStoryFeedbackService.new.call(@story.content)

        if feedback.present?
          current_user.update(ai_feedback_credits: current_user.ai_feedback_credits - 3)
          # Create feedback and get the new record
          new_feedback = @story.feedbacks.create(content: feedback)
          flash[:success] = "Story updated and AI feedback provided!"
          # Set a flag to indicate new feedback was just generated
          flash[:new_feedback] = true
        else
          flash[:success] = "Story updated successfully!"
        end
      else
        flash[:success] = "Story updated successfully!"
      end

      redirect_to edit_story_path(@story)
    else
      flash[:alert] = "Oops, we have a problem. Let's try again."
      render "edit"
    end
  end

  def publish
    @story.update(published: true)
    flash[:success] = "Story was successfully published and is now available to readers!"
    redirect_to edit_story_path(@story)
  end

  def unpublish
    @story.update(published: false)
    flash[:notice] = "Story was successfully unpublished and is no longer visible to others."
    redirect_to edit_story_path(@story)
  end

  def public_stories
    @user = User.friendly.find(params[:user_id])

    @page_title = "#{@user.name}'s shared stories"
    @badge = @user.badges.last

    @pagy, @stories = pagy(@user.stories.published.order(created_at: :desc))
  end

  def show
  end

  private

  def story_params
    params.require(:story).permit(:content, :title)
  end

  def set_story
    @story = Story.friendly.find(params[:id])
  end
end
