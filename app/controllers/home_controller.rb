class HomeController < ApplicationController
  before_action :initialize_attributes, only: [:find_stories, :craft_stories, :share_stories]

  def index
    if user_signed_in?
      redirect_to user_path(current_user)
    end

    headlines = [
      "Craft Stories Like a Pro",
      "Become A Prolific Storyteller",
      "Master Storytelling",
      "Tell Better Stories",
      "Become a Storytelling Expert"
    ]
    @headline = headlines.sample

    @page_title = "Learn to be a storyteller"
    set_meta_tags description: "StoryCoach helps you find, craft, and share your stories. Build confidence and improve your storytelling skills through daily practice and AI-powered feedback.",
                 image_src: ActionController::Base.helpers.asset_url("meta_tags/preview.png")
  end

  def terms
    @page_title = "Terms"
    set_meta_tags description: "Terms of service for StoryCoach, detailing the rules and guidelines for using our storytelling platform and services."
  end

  def privacy
    @page_title = "Privacy Policy"
    set_meta_tags description: "StoryCoach's privacy policy explains how we collect, use, and protect your personal information while using our storytelling platform."
  end

  def homework_for_life
    @page_title = "HomeWork for Life exercise"
    set_meta_tags description: "The Homework for Life exercise helps you capture daily moments that can become powerful stories. Learn how to notice and collect story-worthy moments every day."
  end

  def crash_and_burn
    @page_title = "Crash and Burn exercise"
    set_meta_tags description: "The Crash and Burn exercise helps you write freely without overthinking, building storytelling confidence through uninhibited practice and exploration."
  end

  def about
    @page_title = "About StoryCoach"
    set_meta_tags description: "Learn about StoryCoach, the storytelling platform that helps people find, craft, and share their stories through guided exercises and AI-powered feedback."
  end

  def ai_feedback
    @page_title = "AI Feedback"
    set_meta_tags description: "Get personalized feedback on your stories with StoryCoach's AI-powered analysis. Improve your storytelling with specific, actionable recommendations."
  end

  def faq
    @page_title = "Frequently Asked Questions"
    set_meta_tags description: "Find answers to common questions about StoryCoach, our storytelling exercises, membership options, and how to get the most from your storytelling practice."
  end

  def find_stories
    @page_title = "Find stories"
    set_meta_tags description: "Discover storytelling material hidden in your everyday life. StoryCoach helps you identify meaningful moments and memories worth sharing.",
                 image_src: ActionController::Base.helpers.asset_url("meta_tags/find_stories.png")
  end

  def craft_stories
    @page_title = "Craft stories"
    set_meta_tags description: "Transform your experiences into engaging stories with StoryCoach's guided exercises and AI-powered feedback. Learn storytelling techniques that captivate audiences.",
                 image_src: ActionController::Base.helpers.asset_url("meta_tags/craft_stories.png")
  end

  def share_stories
    @page_title = "Share stories"
    set_meta_tags description: "Build confidence to share your stories with others. StoryCoach helps you refine your storytelling voice and connect with audiences through authentic narratives.",
                 image_src: ActionController::Base.helpers.asset_url("meta_tags/share_stories.png")
  end

  def pricing
    @page_title = "Pricing"
    set_meta_tags description: "Explore StoryCoach membership options. Find the right plan for your storytelling journey, from free exercises to premium features with unlimited AI feedback."
  end

  def storycoach_for_entrepreneurs
    @page_title = "StoryCoach for Entrepreneurs"
    set_meta_tags description: "Elevate your business with powerful storytelling. StoryCoach helps entrepreneurs craft compelling narratives that connect with customers, investors, and teams.",
                  image_src: ActionController::Base.helpers.asset_url("meta_tags/preview_for_entrepreneurs.png")
  end

  def storycoach_for_aspiring_writers
    @page_title = "StoryCoach for Aspiring Writers"
    set_meta_tags description: "Develop your writing skills with StoryCoach. Our platform helps aspiring writers find inspiration, craft engaging narratives, and build confidence in their storytelling abilities.",
                  image_src: ActionController::Base.helpers.asset_url("meta_tags/preview_for_writers.png")
  end

  def storycoach_for_executives
    @page_title = "StoryCoach for Executives"
    set_meta_tags description: "Master leadership storytelling with StoryCoach. Our platform helps executives craft compelling narratives that inspire teams, influence stakeholders, and communicate vision effectively.",
                  image_src: ActionController::Base.helpers.asset_url("meta_tags/preview_for_executives.png")
  end

  private

  def initialize_attributes
    @levels_and_points_map = User::LEVEL_POINTS_AND_CREDITS_MAPPING
  end
end
