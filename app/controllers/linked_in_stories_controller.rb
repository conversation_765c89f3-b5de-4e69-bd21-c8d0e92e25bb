class LinkedInStoriesController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!, except: [:show]
  before_action :set_story, only: [:edit, :show]

  def new
      @user=current_user
      @story = @user.stories.new(category: "linked_in")
      @content_value = params["content"]
  end

  def create
    @user=current_user

    # Create a new story based on the form parameters
    @story = @user.stories.new(story_params)
    @story.update(category: "linked_in")

    if @story.save
      if params[:story][:ai_feedback] == "1"  # "1" means the checkbox was checked
        feedback = AnthropicLinkedinFeedbackService.new.call(@story.content)

        if feedback.present?
          @user.update(ai_feedback_credits: current_user.ai_feedback_credits - 3)
          @story.feedbacks.create(content: feedback)
          # Set flag to indicate new feedback was generated
          flash[:new_feedback] = true
        end
      end

      flash[:success] = "Well done! You have successfully crafted a new LinkedIn post. Share it with your network!"
      redirect_to edit_linked_in_story_path(@story)
    else
      flash[:alert] = "Oops, we have a problem. Let's try again."
      render "new"
    end
  end

  def index
    @stories_drafted = current_user.stories.count

    @page_title = "Write your stories"

    @pagy, @stories = pagy(current_user.stories.order(created_at: :desc))
  end

  def edit
    @ai_feedback_credits = current_user.ai_feedback_credits
    @latest_feedback = @story.feedbacks.last

    # Ensure the feedback content is sanitized for JavaScript display
    if @latest_feedback && @latest_feedback.content
      # Safety measure: sanitize any escaped quotes or characters that might break JS
      @sanitized_feedback_content = @latest_feedback.sanitized_content
    end
  end

  def update
    @story = current_user.stories.friendly.find(params[:id])

    if @story.update(story_params)
      if params[:story][:ai_feedback] == "1"  # "1" means the checkbox was checked
        feedback = AnthropicLinkedinFeedbackService.new.call(@story.content)

        if feedback.present?
          current_user.update(ai_feedback_credits: current_user.ai_feedback_credits - 3)
          # Create feedback and get the new record
          new_feedback = @story.feedbacks.create(content: feedback)
          flash[:success] = "LinkedIn post updated and AI feedback provided!"
          # Set a flag to indicate new feedback was just generated
          flash[:new_feedback] = true
        else
          flash[:success] = "Updated successfully!"
        end
      else
        flash[:success] = "Updated successfully!"
      end

      redirect_to edit_linked_in_story_path(@story)
    else
      flash[:alert] = "Oops, we have a problem. Let's try again."
      render "edit"
    end
  end

  def show
  end

  private

  def story_params
    params.require(:story).permit(:content, :title)
  end

  def set_story
    @story = Story.friendly.find(params[:id])
  end
end
