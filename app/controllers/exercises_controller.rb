class ExercisesController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!
  before_action :get_user_exercises, only: [:index, :search]

  def index
    @page_title = "Practice exercises"

    @pagy, @exercises = pagy(current_user.exercises.order(note_date: :desc))
    @user_able_to_recall_memories = user_able_to_recall_memories?
  end

  def new
      @user=current_user
      @exercise = @user.exercises.new(category: "crash_and_burn")
  end

  def create
    @user=current_user

    # Create a new exercise based on the form parameters
    @exercise = @user.exercises.new(exercise_params)
    @exercise.update(category: "crash_and_burn", note_date: Time.zone.now)

    if @exercise.save
      flash[:notice] = "Successfully completed a Crash and Burn exercise."
      redirect_to exercises_path
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  def update
    @user = current_user

    @exercise = @user.exercises.find(params[:id])

    note_date = DateTime.new(
      params[:exercise][:year].to_i,
      params[:exercise][:month].to_i,
      params[:exercise][:day].to_i
    )

    if @exercise.update(exercise_params.merge(note_date: note_date))
      flash[:notice] = 'Successfully updated!'
      redirect_to exercises_path
    else
      render "edit"
    end
  end

  def edit
    @exercise = current_user.exercises.find(params[:id])
  end

  def search
    user_query = params[:query]
    user_classname = user_class_name
    @user_able_to_recall_memories = user_able_to_recall_memories?

    # Perform semantic search
    response = perform_semantic_search(user_query, user_classname)

    if response.nil?
      flash[:alert] = "I am having trouble finding that memory. Please try again."
    else
      Rails.logger.info response
      @search_result = response.dig("data", "Get", user_classname)

      # Check if the search result is empty or nil
      if @search_result.nil? || @search_result.empty?
        flash[:notice] = "I couldn't find any memories related to \"#{user_query}\". Try a different search term."
        @search_result = nil
      else
        # Format search results
        @search_result = format_search_results(@search_result)
      end
    end

    render :index
  end

  private

  def get_user_exercises
    @pagy, @exercises = pagy(current_user.exercises.order(note_date: :desc))
  end

  def exercise_params
    params.require(:exercise).permit(:note, :photo)
  end

  def user_able_to_recall_memories?
    current_user.premium_user? &&
      current_user.has_ai_credits? &&
      current_user.exercises.homework_for_life.count >= 5
  end

  def user_class_name
    "Exercises_#{current_user.id}"
  end

  def perform_semantic_search(query, class_name)
    # Escape any quotes in the user query to prevent GraphQL syntax errors
    escaped_query = query.gsub('"', '\"')

    # Try hybrid search first
    response = perform_hybrid_search(escaped_query, class_name)

    # If hybrid search fails, fall back to nearText
    if response.nil? || response.dig("data", "Get", class_name).nil?
      response = perform_near_text_search(escaped_query, class_name)
    end

    response
  end

  def perform_hybrid_search(escaped_query, class_name)
    query = build_hybrid_search_query(escaped_query, class_name)
    WeaviateHelper.run_graphql_query(query)
  end

  def perform_near_text_search(escaped_query, class_name)
    query = build_near_text_search_query(escaped_query, class_name)
    WeaviateHelper.run_graphql_query(query)
  end

  def build_hybrid_search_query(escaped_query, class_name)
    # Use hybrid search combining semantic search with keyword matching for better results
    # This combines vector-based similarity with BM25 keyword relevance
    "{
      Get {
        #{class_name} (
          hybrid: {
            query: \"#{escaped_query}\",
            alpha: 0.75,
            properties: [\"memory\"]
          }
          limit: 3
        ) {
          memory
          date
          category
          _additional {
            certainty
          }
        }
      }
    }"
  end

  def build_near_text_search_query(escaped_query, class_name)
    # Fallback to nearText if hybrid search is not supported in your Weaviate instance
    "{
      Get {
        #{class_name} (
          nearText: {
            concepts: [\"#{escaped_query}\"],
            certainty: 0.7
          }
          limit: 3
        ) {
          memory
          date
          category
          _additional {
            certainty
          }
        }
      }
    }"
  end

  def format_search_results(results)
    results.each do |result|
      date_string = result["date"]

      begin
        date = DateTime.parse(date_string)
        # Format the date to be more human-readable
        human_readable_date = date.strftime("%B %d, %Y") # e.g., "June 25, 2024"
        result["date"] = human_readable_date

        # Include match certainty percentage if available
        if result.dig("_additional", "certainty")
          certainty = (result.dig("_additional", "certainty") * 100).round(1)
          result["match_score"] = "#{certainty}% match"
        end
      rescue => e
        Rails.logger.error "Error parsing date: #{e.message}"
        # If date parsing fails, still show the memory but with original date string
      end
    end

    results
  end
end