class MemoryBankExercisesController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!

  def new
      @user=current_user
      @exercise = @user.exercises.new(category: "homework_for_life")
      @daily_prompt = DailyPrompt.all.sample

      if @daily_prompt.nil?
        DailyPrompt.create(
           question: "Did someone say something today that made you laugh? Cry? Become angry? Feel confused?"
        )
      end
  end

  def create
    @user=current_user

    note_date = DateTime.new(
      params[:exercise][:year].to_i,
      params[:exercise][:month].to_i,
      params[:exercise][:day].to_i
    )

    # Create a new exercise based on the form parameters
    @exercise = @user.exercises.new(exercise_params.merge(note_date: note_date))
    @exercise.update(category: "homework_for_life")

    if @exercise.save
      flash[:notice] = "Successfully saved a storyworthy memory."
      flash[:memory_saved] = true
      
      if params[:exercise][:source] == 'users_show'
        redirect_to user_path(@user)
      else
        redirect_to exercises_path
      end
    else
      # Handle validation errors or other issues
      render "new"
    end
  end

  def index
    @pagy, @exercises = pagy(current_user.exercises.order(note_date: :desc))
  end

  private

  def exercise_params
    params.require(:exercise).permit(:note, :photo)
  end
end
