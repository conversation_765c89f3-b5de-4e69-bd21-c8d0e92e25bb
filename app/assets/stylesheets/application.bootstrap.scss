@import 'bootstrap/scss/bootstrap';
@import 'bootstrap-icons/font/bootstrap-icons';

/* Story Generation Loading Overlay */
.story-generation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.story-generation-content {
  text-align: center;
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  max-width: 90%;
  width: 400px;
}
@import "jumpstart/announcements";
@import "landing_page";
@import "social_login";
@import url("https://fonts.googleapis.com/css?family=Roboto:500");

/* Memory Celebration Overlay */
.memory-celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.5s ease-in-out;
}

.celebration-content {
  background-color: white;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  max-width: 90%;
  width: 400px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  animation: scaleIn 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.celebration-icon {
  font-size: 4rem;
  color: #28a745;
  margin-bottom: 1rem;
  animation: bounceIn 0.8s;
}

.celebration-points {
  display: inline-block;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
  margin-top: 1rem;
  font-weight: bold;
  color: #6c757d;
  border: 1px solid #e0e0e0;
  animation: pulseAnimation 2s infinite;
}

.memory-highlight {
  animation: highlightPulse 2s;
  border-left: 4px solid #28a745;
}

.highlight-entry-animation {
  animation: slideIn 0.5s ease-out;
}

.fade-out {
  animation: fadeOut 1s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes scaleIn {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes pulseAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes highlightPulse {
  0% { background-color: rgba(40, 167, 69, 0.2); }
  50% { background-color: rgba(40, 167, 69, 0.1); }
  100% { background-color: transparent; }
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Inspiration Card Styling */
.inspiration-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  border: none;
}

.inspiration-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(108, 117, 125, 0.15);
}

.inspiration-card .card-body {
  background-color: #6c757d; /* More subtle secondary color */
  background-image: linear-gradient(135deg, #6c757d, #5a6268); /* Subtle gradient */
  color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.inspiration-card .card-body::before {
  content: '"';
  position: absolute;
  top: -20px;
  right: 10px;
  font-size: 150px;
  opacity: 0.1;
  font-family: serif;
  line-height: 1;
}

.inspiration-card h6 {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.inspiration-badge {
  display: inline-block;
  padding: 3px 12px;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  letter-spacing: 0.05em;
}

.inspiration-quote p {
  font-style: italic;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-size: 1.25rem;
  font-weight: normal;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

/* StoryCoach Tools Styling */
.storycoach-tools {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.storycoach-tools:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.storycoach-tools .card-header {
  background: linear-gradient(135deg, #0d6efd, #0b5ed7);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  border-bottom: none;
  font-size: 1.1rem;
}

.storycoach-tools .list-group-item {
  border-left: none;
  border-right: none;
  padding: 1rem 1.25rem;
  transition: background-color 0.2s ease;
}

.storycoach-tools .list-group-item:last-child {
  border-bottom: none;
}

.storycoach-tools .list-group-item:hover {
  background-color: #f8f9fa;
}

.storycoach-tool-icon {
  font-size: 1.5rem;
  color: #0d6efd;
  margin-right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(13, 110, 253, 0.1);
  transition: all 0.2s ease;
}

.storycoach-tools .list-group-item:hover .storycoach-tool-icon {
  background-color: rgba(13, 110, 253, 0.2);
  transform: scale(1.05);
}

.storycoach-tool-title {
  color: #0d6efd;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  display: block;
}

.storycoach-tool-desc {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* Navbar Icon Styling */
.navbar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: rgba(13, 110, 253, 0.1);
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
}

.navbar-icon i {
  color: #0d6efd;
  font-size: 1.5rem;
}

.navbar-icon-text {
  font-size: 0.7rem;
  color: #6c757d;
  text-align: center;
  transition: color 0.2s ease;
}

.nav-item:hover .navbar-icon {
  background-color: rgba(13, 110, 253, 0.2);
  transform: translateY(-2px);
}

.nav-item:hover .navbar-icon-text {
  color: #0d6efd;
}

.nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem;
}

/* Style for logout button in navbar */
.nav-item form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Make the button match the nav-link styling */
.nav-item button.nav-link {
  cursor: pointer;
  width: 100%;
  padding: 0.5rem 1rem;
}

/* Adjust spacing for navbar icons on desktop */
@media (min-width: 768px) {
  /* Center the navbar brand and nav items */
  .navbar > .container {
    display: flex;
    justify-content: space-between;
  }

  .navbar-nav {
    display: flex;
    justify-content: center;
    margin: 0 auto;
  }

  .navbar-nav .nav-item {
    margin: 0 1.5rem;
  }

  .navbar-icon {
    margin-bottom: 0.5rem;
    width: 3rem;
    height: 3rem;
  }

  .navbar-icon-text {
    font-weight: 500;
  }
}

/* Premium feature styling */
.premium-feature {
  background-color: rgba(255, 255, 215, 0.2);
  border-left: 3px solid #ffc107;
}

.premium-feature .storycoach-tool-title {
  color: #d69e00;
}

/* Story cards styling */
.story-content {
  color: #4a4a4a;
  line-height: 1.6;
}

.card .badge {
  font-size: 0.7rem;
  padding: 0.3rem 0.6rem;
}

.card h5 {
  color: #333;
  font-weight: 600;
}

/* User Profile Card Styling */
.user-profile-card {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
  border: none;
}

.user-profile-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.user-profile-header {
  background: linear-gradient(135deg, #0d6efd, #0b5ed7);
  padding: 2rem 0;
  position: relative;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.avatar-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
  object-fit: cover;
}

.premium-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background-color: #ffc107;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  color: #212529;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-stats {
  padding: 1rem;
  display: flex;
  justify-content: space-around;
  text-align: center;
  background-color: rgba(13, 110, 253, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0d6efd;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.profile-info {
  padding: 1rem 1.5rem;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.profile-detail {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #6c757d;
}

.profile-detail i {
  width: 24px;
  margin-right: 0.5rem;
  color: #0d6efd;
}

.profile-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
}

.profile-settings-btn {
  display: flex;
  align-items: center;
  color: #6c757d;
  transition: color 0.2s ease;
}

.profile-settings-btn:hover {
  color: #0d6efd;
}

.profile-settings-btn i {
  margin-right: 0.5rem;
}

/* Memory Capture Form Styling */
.memory-capture-form {
  margin-bottom: 0;
}

.memory-capture-form label {
  color: #495057;
  font-size: 0.9rem;
}

.memory-capture-form .form-control {
  border-color: #dee2e6;
  box-shadow: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.memory-capture-form .form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.memory-capture-form .input-group-text {
  color: #6c757d;
  border-color: #dee2e6;
}

.memory-capture-form .form-select {
  border-color: #dee2e6;
  margin-right: 0.5rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.memory-capture-form .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.memory-capture-form .form-select:last-child {
  margin-right: 0;
}

.memory-capture-form .btn-primary {
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 0.5rem;
}