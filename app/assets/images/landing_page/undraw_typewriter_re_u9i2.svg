<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="722.11262" height="558.1509" viewBox="0 0 722.11262 558.1509" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M892.0771,705.04148h-585.082a68.4964,68.4964,0,0,1-66.89649-83.21289l26.13379-118.78711H932.83979l26.13379,118.78711a68.49639,68.49639,0,0,1-66.89648,83.21289Zm-624.23731-200-25.78808,117.2168a66.49673,66.49673,0,0,0,64.94336,80.7832h585.082a66.49674,66.49674,0,0,0,64.94336-80.7832l-25.78809-117.2168Z" transform="translate(-238.47977 -171.03678)" fill="#f2f2f2"/><path d="M817.7855,249.41514l30.69046-4.5739-4.84758-21.34916-18.4,9.26659-37.961-14.30669a7.71684,7.71684,0,1,0-5.0485,10.199Z" transform="translate(-238.47977 -171.03678)" fill="#ffb6b6"/><path d="M880.164,233.45546c-2.37275,8.99484-57.04774,19.14085-57.02046,17.19492.07752-5.53051,3.5933-17.135,1.58543-20.14555-1.14827-1.72168,14.31267-6.121,14.31267-6.121s8.53341-3.28453,19.59642-7.29317a19.72107,19.72107,0,0,1,18.85049,2.60441S882.5367,224.46062,880.164,233.45546Z" transform="translate(-238.47977 -171.03678)" fill="#6c63ff"/><path d="M351.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M412.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M473.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M534.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M595.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M656.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M717.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M778.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M839.24671,608.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M351.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M412.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M473.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#6c63ff"/><path d="M534.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M595.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M656.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M717.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M778.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M839.24671,541.03678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M379.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M440.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M501.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M562.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M623.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M684.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M745.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M806.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M867.74671,575.53678h-19a16,16,0,0,0,0,32h19a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M797.37855,504.03678H402.61488a23.64479,23.64479,0,0,1-23.61817-23.61816l.02588-.22559c13.96582-60.42773,13.96045-136.18164-.0166-238.40234l-.00928-.13574a23.64512,23.64512,0,0,1,23.61817-23.61817H797.37855A23.64511,23.64511,0,0,1,820.99671,241.655l-.022.209a566.87235,566.87235,0,0,0,0,238.3457l.022.209A23.64478,23.64478,0,0,1,797.37855,504.03678Z" transform="translate(-238.47977 -171.03678)" fill="#fff"/><path d="M797.37855,504.03678H402.61488a23.64479,23.64479,0,0,1-23.61817-23.61816l.02588-.22559c13.96582-60.42773,13.96045-136.18164-.0166-238.40234l-.00928-.13574a23.64512,23.64512,0,0,1,23.61817-23.61817H797.37855A23.64511,23.64511,0,0,1,820.99671,241.655l-.022.209a566.87235,566.87235,0,0,0,0,238.3457l.022.209A23.64478,23.64478,0,0,1,797.37855,504.03678ZM380.9972,480.53092a21.64307,21.64307,0,0,0,21.61768,21.50586H797.37855a21.64279,21.64279,0,0,0,21.61767-21.51367,568.84463,568.84463,0,0,1,0-238.97266,21.64279,21.64279,0,0,0-21.61767-21.51367H402.61488a21.64308,21.64308,0,0,0-21.61817,21.55078C394.98939,343.95622,394.99036,419.89274,380.9972,480.53092Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M755.99671,288.53678h-319a6.5,6.5,0,0,1,0-13h319a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M755.99671,320.03678h-319a6.5,6.5,0,0,1,0-13h319a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M755.99671,351.53678h-319a6.5,6.5,0,0,1,0-13h319a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M755.99671,383.03678h-319a6.5,6.5,0,0,1,0-13h319a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M755.99671,414.53678h-319a6.5,6.5,0,0,1,0-13h319a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M579.99671,446.03678h-143a6.5,6.5,0,0,1,0-13h143a6.5,6.5,0,0,1,0,13Z" transform="translate(-238.47977 -171.03678)" fill="#6c63ff"/><path d="M598.17835,495.53678H571.85511a2.65765,2.65765,0,0,1-2.06885-1.01953,3.174,3.174,0,0,1-.60058-2.65234l12.3872-56.0459a2.69956,2.69956,0,0,1,5.32032-.08106l13.936,56.04493a3.1748,3.1748,0,0,1-.55762,2.7041A2.65706,2.65706,0,0,1,598.17835,495.53678Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/><path d="M928.99671,516.53678h-658a13.5,13.5,0,0,1,0-27h658a13.5,13.5,0,0,1,0,27Z" transform="translate(-238.47977 -171.03678)" fill="#f2f2f2"/><polygon points="607.193 311.693 597.591 311.692 593.024 274.657 607.195 274.658 607.193 311.693" fill="#ffb6b6"/><path d="M848.12137,492.03678l-30.95975-.00114v-.39159a12.051,12.051,0,0,1,12.0504-12.05021h.00076l5.65521-4.29034,10.55135,4.291,2.7026.0001Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><polygon points="693.95 299.209 685.357 303.492 664.747 272.384 677.431 266.063 693.95 299.209" fill="#ffb6b6"/><path d="M938.77356,477.48361l-27.709,13.81-.17469-.35047a12.051,12.051,0,0,1,5.40937-16.16039l.00069-.00034,3.14742-6.36255,11.3575-.86655,2.41883-1.20552Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M841.41862,314.52925l52.94493,1.85308,3.95881,7.35208s4.14634,30.07924,2.02286,32.20272-3.18522,2.12348-2.12348,5.83957,3.80663,39.22214,3.80663,39.22214,21.87145,45.36626,22.93319,48.02061,2.12348,1.59261,1.06174,2.65435A39.09813,39.09813,0,0,0,923.369,454.859H906.0645s-9.31724-23.50265-17.846-32.59349a51.63525,51.63525,0,0,1-13.61219-29.178l-7.175-33.96517L852.036,411.14763s-2.65436,51.49442-2.12349,54.14877l.53087,2.65435-20.39562.22781s-2.12348-4.47477-1.06174-6.06738.998-1.15789-.29729-3.76417-.76445-7.02253-.76445-7.02253,7.65474-104.41215,7.65474-106.00476a5.29511,5.29511,0,0,0-.441-2.38825V340.7697l2.03361-7.66Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M864.24632,218.46691c-3.44619,2.04877-5.50772,5.81313-6.69112,9.64368a91.38867,91.38867,0,0,0-3.9272,21.83492l-1.24993,22.18655a91.3502,91.3502,0,0,1-11.627,40.78356,7.12994,7.12994,0,0,0,4.45968,10.32456c19.18914,5.03527,55.533-1.28668,55.533-1.28668s1.548-.516,0-2.064-5.02085-9.28825-5.02085-9.28825l4.12808-27.117,5.16009-54.69713c-6.19215-7.74017-18.64533-12.30294-18.64533-12.30294l-3.21775-5.79194-16.08872,1.28709Z" transform="translate(-238.47977 -171.03678)" fill="#6c63ff"/><circle cx="635.97946" cy="20.8342" r="15.4554" fill="#ffb8b8"/><path d="M893.50263,190.95324a26.87734,26.87734,0,0,1-3.67594,8.807,5.99486,5.99486,0,0,1-2.00508,2.27656,2.19153,2.19153,0,0,1-2.78481-.34811l-.376-.30633a7.97635,7.97635,0,0,0,1.9494-1.69177,2.71626,2.71626,0,0,0,.52216-2.42975,1.67252,1.67252,0,0,0-2.04685-1.093c-.9051.35507-1.45508,1.66393-2.40191,1.46205-.76585-.16712-.926-1.17659-.926-1.97027.02783-4.1494-1.97724-10.04623-3.52977-9.86521a12.51866,12.51866,0,0,1-4.62973-.61963,12.12965,12.12965,0,0,0-4.63675-.5918c-.11138.01394-.22279.03484-.34114.05571a10.267,10.267,0,0,0-1.03036-2.24873,12.01386,12.01386,0,0,1-.12532,2.54809,23.83181,23.83181,0,0,1-3.857,1.21836c-1.43418.20192-4.588,4.49053-4.73418,4.05191a10.2676,10.2676,0,0,0-1.03037-2.24872,12.01386,12.01386,0,0,1-.12531,2.54809c-.007.0348-.007.06267-.01394.09748-.69621-.926-1.12088-2.05382-.88419-1.19053-2.33227-5.2772-1.42722-9.05759,2.2418-13.50635a7.05517,7.05517,0,0,1,2.75-2.207,3.46792,3.46792,0,0,1,3.38359.33421,11.38389,11.38389,0,0,1,11.29242-2.40888c3.82216,1.32977,6.76713,2.10952,7.35192,6.11966a8.50874,8.50874,0,0,1,8.15257,3.96834A12.84965,12.84965,0,0,1,893.50263,190.95324Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M909.99671,291.38527l.02514-31.02941-21.83183,1.63,6.43726,19.57017-19.77475,35.42138a7.71685,7.71685,0,1,0,9.33816,6.5043Z" transform="translate(-238.47977 -171.03678)" fill="#ffb6b6"/><path d="M903.45817,227.33035c8.54385,3.67964,10.47463,59.25452,8.55424,58.93915-5.458-.89632-16.41325-6.09313-19.68811-4.55362-1.87284.88042-3.93214-15.06177-3.93214-15.06177s-1.98355-8.926-4.30831-20.4609a19.721,19.721,0,0,1,5.36941-18.25633S894.91432,223.65073,903.45817,227.33035Z" transform="translate(-238.47977 -171.03678)" fill="#6c63ff"/><path d="M375.81748,570.22737a5.23881,5.23881,0,0,1,.34211-8.02572l-5.8656-17.66857,9.34295,2.51,4.1377,16.39356a5.26714,5.26714,0,0,1-7.95716,6.79069Z" transform="translate(-238.47977 -171.03678)" fill="#ffb6b6"/><path d="M392.75058,474.23094c1.55025.88871,10.72239-1.31335,12.51367-.68693-.98881,6.03322,4.38179,3.76921-1.45026,11.69239s-17.57849,31.679-20.24693,40.55766-.02734,21.391-.427,25.599a43.66668,43.66668,0,0,0,.036,7.32415c-3.151-.03982-6.25679.61406-9.457.19641-3.41282-9.657-5.70752-23.40286-7.26568-30.99974s-1.61516-4.38419-.90051-8.18286.88769-1.03827.137-4.67233,2.28537-8.2,5.09062-10.80348c1.20952-3.47889,2.77265-6.72266,3.97008-10.16523C383.52546,486.4351,384.1024,482.01317,392.75058,474.23094Z" transform="translate(-238.47977 -171.03678)" fill="#3f3d56"/><path d="M474.88381,559.02692a5.23879,5.23879,0,0,1-5.97865-5.36516l-17.36876-6.70149,7.87608-5.61769,15.28618,7.2251a5.26715,5.26715,0,0,1,.18515,10.45924Z" transform="translate(-238.47977 -171.03678)" fill="#ffb6b6"/><path d="M411.53417,484.94c1.67143-.632,5.80107-9.11275,7.4232-10.09752,4.02934,4.598,5.69494-.98721,8.1051,8.55116s13.28449,33.70588,18.443,41.40914,16.49713,13.61695,19.49177,16.6a43.66635,43.66635,0,0,0,5.67736,4.62732c-2.03347,2.40735-3.50266,5.22071-5.85913,7.42595-9.62465-3.50308-21.69532-10.46818-28.55069-14.0937s-4.4113-1.53959-6.88976-4.50569-.23737-1.34524-3.52012-3.07542-4.87812-6.9762-5.10508-10.79665c-1.91706-3.14492-3.42783-6.4134-5.32453-9.52591C415.09278,499.81889,412.04562,496.563,411.53417,484.94Z" transform="translate(-238.47977 -171.03678)" fill="#3f3d56"/><polygon points="186.123 366.39 194.791 405.071 139.247 401.616 154.351 363.627 186.123 366.39" fill="#ffb6b6"/><polygon points="176.874 544.917 183.113 544.916 186.081 520.851 176.873 520.851 176.874 544.917" fill="#ffb6b6"/><path d="M413.24084,712.87544l9.86294-.58869v4.22572l9.377,6.47607a2.63953,2.63953,0,0,1-1.49987,4.81163h-11.7422l-2.024-4.17987-.79025,4.17987h-4.42727Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><polygon points="117.905 541.813 124.002 543.135 132.005 520.246 123.006 518.294 117.905 541.813" fill="#ffb6b6"/><path d="M354.97208,709.39342l9.76355,1.51568-.89587,4.12965,7.79087,8.31682a2.63954,2.63954,0,0,1-2.48586,4.38428l-11.47529-2.48939-1.09179-4.51394-1.65844,3.91732-4.32664-.93859Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M434.68851,608.453c-4.37873,24.06477-5.4734,34.45515-5.4734,34.45515s3.28406,3.82832,1.09467,6.01771,0,6.562,0,6.562l-4.923,53.05259-3.96291-.64217-9.5464-1.535-3.44307-.5565,2.18938-49.76853s-2.18938-4.37873-1.09467-5.47345c1.09467-1.09467-1.09471-7.65667-1.09471-7.65667l-2.73365-51.95791-16.95847,56.88094s1.64506,2.18939,0,3.82836c-1.639,1.639-4.91694,9.846-4.91694,9.846l-10.08457,35.28686-.8562,2.99661-5.25938-.9846-5.72416-1.07634-1.0519-.19567-1.87136-.35471-1.20475-.22628-.40977-.07338-1.98144-.37308.59935-2.66026.31186-1.40045,8.53123-37.8921.1957-.87452.20795-.92346s-1.09471-2.73364.54426-4.37873a4.27375,4.27375,0,0,0,1.09471-3.82836,1.49325,1.49325,0,0,1-.14677-.18346,2.27029,2.27029,0,0,1-.3914-.89288,2.96057,2.96057,0,0,1,1.08855-2.752,4.66973,4.66973,0,0,0,.77669-1.113c1.66345-3.14953,2.50128-9.82161,2.50128-9.82161s1.48-52.84464,6.32348-60.9172c.25689-.422,5.39871-8.523,5.66779-8.68811,5.46733-3.27793,48.06278,1.24967,48.06278,1.24967s2.57993,6.93083,2.69613,7.65247C434.07529,573.13556,438.40067,588.03921,434.68851,608.453Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M421.7457,481.16455s.92165-4.78429-7.93483-13.64077c-4.68873-1.56291-12.81266-.652-15.62908,0-13.02424,17.192-10.69228,14.15858-14.08253,23.7643a13.52866,13.52866,0,0,0-.928,6.83773c1.5629,7.81454,4.10025,47.92846,3.57928,50.01234s-4.16776,4.68872-.521,4.68872,50.67527-.82322,49.63333-2.38613S421.7457,481.16455,421.7457,481.16455Z" transform="translate(-238.47977 -171.03678)" fill="#3f3d56"/><circle cx="168.57659" cy="278.54491" r="14.93754" fill="#ffb6b6"/><path d="M394.40092,455.22963c-.08162,1.81151-1.21709,3.37347-2.09351,4.95938a19.78854,19.78854,0,0,0-.18369,18.45966c1.21928,2.31278,2.90747,4.37677,3.8749,6.80567a7.73112,7.73112,0,0,1-.06058,6.52172c-2.61823-2.51922-3.89582-1.189-6.869-3.388,1.16921,3.27673.17249,3.38345,1.34621,6.65666q-7.56916,2.16111-15.14392,4.33382a18.25213,18.25213,0,0,0,5.22148-21.48155c-1.846-3.95845-5.14554-7.22129-6.34675-11.4272a12.39871,12.39871,0,0,1,17.04247-14.71269C392.88618,451.472,394.4831,453.44685,394.40092,455.22963Z" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M413.16307,432.8393c-2.0088-4.38928-7.309-3.4962-12.10128-2.91722a14.894,14.894,0,0,0-11.15917,8.28094,20.35278,20.35278,0,0,0-1.10354,14.1278,15.47841,15.47841,0,0,0,4.88706,8.23865c2.53091,2.03537,14.42859,6.84906,15.419,2.70413-1.23813,5.40754-4.45963,8.48642-.77251,12.63124,1.90974-5.73678,9.9854-11.18566,11.89514-16.92244.28747-.86355-.89305-11.01274-.60558-11.87629.606-1.82026,1.21278-3.64358,1.65064-5.51142.55-2.34638.76172-5.03695-.69576-6.95627s-5.12912-1.994-5.86577.30066" transform="translate(-238.47977 -171.03678)" fill="#2f2e41"/><path d="M410.75815,451.10681l-.63851-4.06484c.43393-5.05554-.27442-13.097,3.04343-14.20267l2.029,2.029C413.90857,435.29624,411.46749,442.85627,410.75815,451.10681Z" transform="translate(-238.47977 -171.03678)" fill="#ff6584"/><path d="M556.26874,727.99768a1.18647,1.18647,0,0,1-1.19006,1.19h-280.29a1.19,1.19,0,1,1,0-2.38h280.29A1.18651,1.18651,0,0,1,556.26874,727.99768Z" transform="translate(-238.47977 -171.03678)" fill="#ccc"/><path d="M702.99671,654.04148h-206a16,16,0,0,0,0,32h206a16,16,0,0,0,0-32Z" transform="translate(-238.47977 -171.03678)" fill="#e6e6e6"/></svg>