class RemoveExerciseFromWeaviateJob < ApplicationJob
  queue_as :default
  
  # This job doesn't need retries as much since deletion is less critical than creation
  # Still, we'll add some retries for network issues
  retry_on StandardError, wait: :exponentially_longer, attempts: 2
  
  # We need to pass all the exercise attributes because the record might be deleted
  # by the time this job runs
  def perform(exercise_attributes)
    # Skip if we don't have the necessary attributes
    return unless exercise_attributes && 
                  exercise_attributes["id"] && 
                  exercise_attributes["user_id"] && 
                  exercise_attributes["note"] && 
                  exercise_attributes["category"]
    
    # Only process exercises in the searchable categories
    return unless WeaviateServices::SEARCHABLE_CATEGORIES.include?(exercise_attributes["category"])
    
    # Create a temporary exercise-like object that has the methods needed by WeaviateServices
    exercise = OpenStruct.new(
      id: exercise_attributes["id"],
      user_id: exercise_attributes["user_id"],
      note: exercise_attributes["note"],
      category: exercise_attributes["category"]
    )
    
    Rails.logger.info "Removing exercise ID #{exercise.id} from Weaviate in background job"
    WeaviateServices.remove_exercise_from_weaviate(exercise)
  end
end