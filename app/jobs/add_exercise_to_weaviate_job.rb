class AddExerciseToWeaviateJob < ApplicationJob
  queue_as :default
  
  # Retry failed jobs up to 3 times with exponential backoff
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  
  # Process when the exercise gets saved to the database
  # This adds the exercise to the Weaviate vector database for semantic search
  def perform(exercise_id)
    exercise = Exercise.find_by(id: exercise_id)
    return unless exercise # Skip if exercise doesn't exist anymore
    
    # Only process exercises in the searchable categories
    return unless WeaviateServices::SEARCHABLE_CATEGORIES.include?(exercise.category)
    
    Rails.logger.info "Adding exercise ID #{exercise_id} to Weaviate in background job"
    WeaviateServices.add_exercise_to_weaviate(exercise)
  end
end