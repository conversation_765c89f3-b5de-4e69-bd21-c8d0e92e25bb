require "net/http"
require "uri"
require "json"

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.run_graphql_query(query)
    weaviate_url = ENV["WEAVIATE_URL"]
    api_key = ENV["WEAVIATE_API_KEY"]
    openai_api_key = ENV["OPENAI_WEAVIATE_KEY"]
    # cohere_api_key = ENV["COHERE_APIKEY"]

    uri = URI.parse("#{weaviate_url}/v1/graphql")

    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == "https"

    request = Net::HTTP::Post.new(uri.request_uri)
    request["Authorization"] = "Bearer #{api_key}"
    request["Content-Type"] = "application/json"
    request["X-OpenAI-Api-Key"] = openai_api_key
    # request["X-Cohere-Api-Key"] = cohere_api_key
    request.body = { query: query }.to_json

    begin
      response = http.request(request)
      if response.code == "200"
        return JSON.parse(response.body)
      else
        Rails.logger.error "GraphQL query failed: #{response.body}"
        return nil
      end
    rescue => e
      Rails.logger.error "GraphQL request failed with error: #{e.message}"
      return nil
    end
  end
end