class UserMailer < ApplicationMailer

  default from:  "<EMAIL>"

  # Subject can be set in your I18n file at config/locales/en.yml
  # with the following lookup:
  #
  #   en.user_mailer.welcome.subject
  #
  def welcome(user)
    @user = user
    @url = root_url
    @application_name = Rails.configuration.application_name
    @ai_feedback_credits_allocated = User::DEFAULT_AI_CREDITS_ALLOCATION
    
    bootstrap_mail to: @user.email
  end

  def first_badge(user)
    @user = user
    @url = root_url
    @application_name = Rails.configuration.application_name
    
    bootstrap_mail to: @user.email
  end

  def first_exercise_badge(user)
    @user = user
    @url = root_url
    @application_name = Rails.configuration.application_name
    
    bootstrap_mail to: @user.email
  end

  def first_story_badge(user)
    @user = user
    @url = root_url
    @application_name = Rails.configuration.application_name
    
    bootstrap_mail to: @user.email
  end

  def next_level(user)
    @user = user
    @url = root_url
    @application_name = Rails.configuration.application_name
    
    bootstrap_mail to: @user.email
  end
end
