class ReminderMailers < ApplicationMailer
  default from:  "<EMAIL>",
          reply_to: <PERSON><PERSON><PERSON>["POSTMARK_INBOUND_EMAIL"]

  def crash_and_burn_mailer(user)
    @user = user
    @url = crash_and_burn_url
    @crash_and_burn_exercise_url = new_exercise_url

    bootstrap_mail(
      to: @user.email,
      subject: "Crash and Burn - #{Time.now.strftime("%m/%d/%Y")}"
    )
  end

  def h4l_mailer(user)
    @user = user
    @url = homework_for_life_url

    @daily_prompt = DailyPrompt.all.sample

    @daily_inspiration = Resource.all.sample

    @highlight_memory = highlight_memory

    bootstrap_mail(
      to: @user.email,
      subject: "StoryCoach's reminder to save a memory from the day - #{Time.now.strftime("%m/%d/%Y")}"
    )
  end

  def weekly_stats(user)
    @user = user
    @url = root_url

    @user_level = @user.level
    @points_to_level_up =  points_to_next_level(@user)
    @badge = @user.badges.last
    @exercises_done = @user.exercises.where(created_at: 1.week.ago..Time.zone.now).count
    @stories_created = @user.stories.where(created_at: 1.week.ago..Time.zone.now).count
    @week_days = 1.week.ago.strftime("%B, %d") + " - " + Time.zone.now.strftime("%B, %d")

    bootstrap_mail(
      to: @user.email,
      subject: "🏁 See your #{Rails.configuration.application_name} stats!",
    )

  end

  private

  def highlight_memory
    if Date.today.strftime("%d").to_i % 5 == 0
      return @user.exercises.homework_for_life.sample
    end
  end

  def points_to_next_level(user)
    next_level = user.level + 1
    User::LEVEL_POINTS_AND_CREDITS_MAPPING[next_level][:points] - user.points
  end
end
