require "anthropic"

class AnthropicDraftAStoryService
  def initialize
    @client = Anthropic::Client.new(access_token: ENV["ANTHROPIC_ACCESS_TOKEN"])
  end

  def call(memory)
    prompt = "You are a skilled writer tasked with transforming a personal memory into a structured, realistic short story. Your goal is to create a narrative that closely follows the provided memory while adhering to the CART framework and maintaining realism.\n\nHere is the memory you will be working with:\n\n<memory>\n#{memory}\n</memory>\n\nBefore drafting the story, please analyze the memory and plan your approach using the CART framework:\n\n<CART_analysis>\n1. Context:\n   - Identify the setting (time and place)\n   - Describe the main character (the narrator)\n   - State their primary goal or desire\n\n2. Adversity:\n   - Determine the main challenge or problem present in the memory\n   - List any secondary obstacles or complications\n\n3. Resolution:\n   - Identify one or two key actions taken to overcome the challenge\n   - Explain how these actions directly address the adversity\n\n4. Takeaway:\n   - Consider how the experience affected or transformed the narrator\n   - Reflect on any lessons learned or personal growth\n\n5. Concrete Details:\n   - List 5-10 specific details from the memory (e.g., sensory descriptions, dialogue snippets, precise actions)\n   - Explain how these details contribute to the realism of the story\n\nAnalyze how you can present these elements realistically, focusing on concrete details from the memory and avoiding any embellishments or fantastical elements.\n</CART_analysis>\n\nNow, draft a short story based on your analysis. The story should:\n- Be written in the first person\n- Contain 250 or fewer words\n- Follow the CART framework\n- Maintain a realistic tone and focus on actual events from the memory\n- Avoid excessive creativity or embellishments\n\nPresent your final story within <story> tags.\n\nAfter writing the story, please count the words and ensure it meets the 250-word limit. If it exceeds 250 words, revise it to fit within the limit while maintaining the key elements of the CART framework.\n\nRemember to review your story to ensure it adheres to all the requirements before submitting your final version."

    response = generate_story(prompt)

    Rails.logger.info "AnthropicDraftAStoryService: #{response}"

    # Extract the story and CART analysis from the response
    story = extract_story(response)
    cart_analysis = extract_cart_analysis(response)

    return { story: story, cart_analysis: sanitize_text(cart_analysis) }
  end

  private

  def extract_cart_analysis(response)
    return nil if response.nil?

    # Extract content between <CART_analysis> tags
    cart_match = response.match(/(.*?)<\/CART_analysis>/m)
    return cart_match[1].strip if cart_match

    # If no CART analysis tags found, return nil
    nil
  end

  def generate_story(prompt, max_tokens_to_sample=20000)
    begin
      response = @client.messages(
        parameters: {
          model: "claude-3-7-sonnet-20250219",
          messages: [
            {
              role: "user",
              content: prompt
            },
            {
              role: "assistant",
              content: "<CART_analysis>"
            }
          ],
          temperature: 1,
          max_tokens: max_tokens_to_sample
        }
      )
      return response["content"][0]["text"]
    rescue StandardError => e
      puts "Error generating story: #{e.message}"
      return nil
    end
  end

  def extract_story(response)
    return nil if response.nil?

    # Extract content between <story> tags
    story_match = response.match(/<story>(.*?)<\/story>/m)
    return story_match[1].strip if story_match

    # If no story tags found, return the whole response
    response
  end

  def sanitize_text(text)
    return nil if text.nil?

    # Replace escape sequences that cause problems
    sanitized = text.gsub(/\\"/,'"')  # Replace \" with simple "
                  .gsub(/\\'/,"'")    # Replace \' with simple '
                  .gsub(/\\n/,"\n")   # Replace \n with actual newlines
                  .gsub(/\\t/,"\t")   # Replace \t with actual tabs

    sanitized
  end
end