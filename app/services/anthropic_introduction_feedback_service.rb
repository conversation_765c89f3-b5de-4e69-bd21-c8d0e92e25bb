require "anthropic"

class AnthropicIntroductionFeedbackService
  def initialize
    @client = Anthropic::Client.new(access_token: ENV["ANTHROPIC_ACCESS_TOKEN"])
  end

  def call(content)
    prompt = "In 100 words or less, provide feedback on how to make the following " +
             "introduction better by (1) Keeping it simple (2) Stating what business you " +
             "are in (3) Articulating the most obvious need or problem your customer has " +
             "(4) Sharing how you solve it: " +
             "\n#{content}"
      
    generated_text = generate_text(prompt)

    return strip_brackets_and_newlines(generated_text)
  end

  private

  def generate_text(prompt, max_tokens_to_sample=1000)
    begin
      response = @client.complete(
        parameters: {
            model: "claude-2.1",
            prompt: prompt,
            max_tokens_to_sample: max_tokens_to_sample
        })
      return response["completion"]    
    rescue StandardError => e
      puts "Error generating text: #{e.message}"
      return nil
    end
  end

  def strip_brackets_and_newlines(input_string)
    # Remove "[" and "]" characters
    cleaned_string = input_string.gsub(/[\[\]]/, '')
  
    # Remove "\n" characters
    cleaned_string.gsub!("\n", '')
  
    return cleaned_string
  end
end