class OpenaiStoryFeedbackService
  def initialize
    @open_ai_client = OpenAI::Client.new(access_token: ENV["OPENAI_ACCESS_TOKEN"])
  end

  def call(story_content)
    prompt = "I want to use the techniques described by <PERSON> in the book Storyworthy to craft a story. " +
             "The keys to a good story are: " +
             "(1) Start with action and location (2) Have a single takeaway (3) A transformation " +
             "(4) A five-second moment that triggers a realization" +
             "In 150 words or less, provide feedback on how to make the following story better by <PERSON> tips from Storyworth. " +
             "Don't write the story, just provide suggestions." +
             "\n#{story_content}"

    generated_text = generate_text(prompt)

    return strip_brackets_and_newlines(generated_text)
  end

  private

  def generate_text(prompt, temperature=0.7)
    begin
      response = @open_ai_client.chat(
        parameters: {
            model: "gpt-3.5-turbo", # Required.
            messages: [{ role: "user", content: prompt}], # Required.
            temperature: temperature,
        })

      return response.dig("choices", 0, "message", "content")
    rescue StandardError => e
      puts "Error generating text: #{e.message}"
      return nil
    end
  end

  def strip_brackets_and_newlines(input_string)
    # Remove "[" and "]" characters
    cleaned_string = input_string.gsub(/[\[\]]/, '')

    # Remove "\n" characters
    cleaned_string.gsub!("\n", '')

    return cleaned_string
  end
end