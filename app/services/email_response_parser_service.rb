class EmailResponseParserService
    def initialize
    end

    def call(email_text)
        lines = email_text.split("\n")
        on_line_index = lines.index { |line| line.match?(/On (.+), (.+) wrote:/) }
      
        if on_line_index
          receiver_reply = lines[0..(on_line_index - 1)].join("\n")
          receiver_reply.strip
        else
          # If "On" line is not found, assume the entire email is the receiver's reply
          email_text.strip
        end
    end
end