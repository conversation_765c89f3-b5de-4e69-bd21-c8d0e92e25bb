class PointsAndLevelAllocationService
  def initialize(user, points:)
    @user = user
    @points_gained = points
  end

  def call
    @user.update(points: @user.points + @points_gained)

    next_level = @user.level + 1

    if @user.points >= User::LEVEL_POINTS_AND_CREDITS_MAPPING[next_level][:points]
      Rails.logger.info "User #{@user.email} is promoted to next level #{next_level} with #{@user.points}"

      new_credits = @user.premium_user? ?  User::LEVEL_POINTS_AND_CREDITS_MAPPING[next_level][:credits] : 0

      @user.update(
        level: next_level,
        ai_feedback_credits: @user.ai_feedback_credits + new_credits,
      )

      Rails.logger.info "Sending UserMailer.next_level to #{@user.email}"
      UserMailer.next_level(@user).deliver_later
    end
  end
end