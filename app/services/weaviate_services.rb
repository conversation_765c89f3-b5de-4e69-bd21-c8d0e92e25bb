require "weaviate"

class WeaviateServices
  SEARCHABLE_CATEGORIES = ["homework_for_life", "random_word", "memory_inspiration"].freeze
  
  def initialize
  end

  def self.create_classes
    User.find_each do |user|
      WEAVIATE_CLIENT.schema.delete(class_name: "Exercises_#{user.id}") rescue nil

      WEAVIATE_CLIENT.schema.create(
        class_name: "Exercises_#{user.id}",
        description: "Notes from exercises of a user",
        properties: [
            {
                "dataType": ["text"],
                "description": "date of memory",
                "name": "date"
            }, {
                "dataType": ["text"],
                "description": "memory captured on that day",
                "name": "memory"
            }, {
                "dataType": ["text"],
                "description": "The category",
                "name": "category"
            }
        ],
        vectorizer: "text2vec-openai"
      )
    end
  end

  def self.populate_schemas_with_data
    User.find_each do |user|
      # Find all exercises that are searchable
      searchable_exercises = user.exercises.where(category: SEARCHABLE_CATEGORIES)
      
      searchable_exercises.find_in_batches do |group|
        objects = group.map do |exercise|
          create_weaviate_object(exercise)
        end

        WEAVIATE_CLIENT.objects.batch_create(
          objects: objects
        ) unless objects.empty?
      end
    end
  end
  
  # Add a single exercise to Weaviate
  def self.add_exercise_to_weaviate(exercise)
    return unless exercise && SEARCHABLE_CATEGORIES.include?(exercise.category)
    
    # Create the class for this user if it doesn't exist
    ensure_class_exists(exercise.user)
    
    begin
      # Create a single object in Weaviate
      object = create_weaviate_object(exercise)
      
      WEAVIATE_CLIENT.objects.create(
        class_name: "Exercises_#{exercise.user_id}",
        properties: object[:properties]
      )
      
      Rails.logger.info "Added exercise ID #{exercise.id} to Weaviate for user #{exercise.user_id}"
      true
    rescue => e
      Rails.logger.error "Failed to add exercise ID #{exercise.id} to Weaviate: #{e.message}"
      false
    end
  end
  
  # Ensure the class exists for a user
  def self.ensure_class_exists(user)
    begin
      # Check if the class exists
      WEAVIATE_CLIENT.schema.get(class_name: "Exercises_#{user.id}")
    rescue => e
      # If not, create it
      Rails.logger.info "Creating Weaviate class for user #{user.id}"
      create_class_for_user(user)
    end
  end
  
  # Create a class for a specific user
  def self.create_class_for_user(user)
    WEAVIATE_CLIENT.schema.create(
      class_name: "Exercises_#{user.id}",
      description: "Notes from exercises of a user",
      properties: [
          {
              "dataType": ["text"],
              "description": "date of memory",
              "name": "date"
          }, {
              "dataType": ["text"],
              "description": "memory captured on that day",
              "name": "memory"
          }, {
              "dataType": ["text"],
              "description": "The category",
              "name": "category"
          }
      ],
      vectorizer: "text2vec-openai"
    )
  end
  
  # Create a Weaviate object from an exercise
  def self.create_weaviate_object(exercise)
    {
      class: "Exercises_#{exercise.user_id}",
      properties: {
        date: exercise.note_date,
        memory: exercise.note,
        category: exercise.category
      }
    }
  end
  
  # Remove an exercise from Weaviate
  def self.remove_exercise_from_weaviate(exercise)
    return unless exercise && SEARCHABLE_CATEGORIES.include?(exercise.category)
    
    begin
      # We need to find the object's UUID in Weaviate first
      # Since we don't store the Weaviate UUID, we need to search for it by properties
      class_name = "Exercises_#{exercise.user_id}"
      
      # Search for objects that match this exercise's content
      # This is a simplification - in a production system, you'd want to use a more 
      # reliable way to track which Weaviate object corresponds to which Exercise
      where_filter = {
        operator: "And",
        operands: [
          {
            path: ["memory"],
            operator: "Equal",
            valueText: exercise.note
          },
          {
            path: ["category"],
            operator: "Equal",
            valueText: exercise.category
          }
        ]
      }
      
      # Query Weaviate to find objects that match our exercise
      result = WEAVIATE_CLIENT.query.get(
        class_name: class_name,
        fields: "_additional { id }",
        where: where_filter.to_json
      )
      
      # Extract object IDs
      objects = result["data"]["Get"][class_name]
      
      # Delete each matching object
      if objects && objects.any?
        objects.each do |obj|
          if obj["_additional"] && obj["_additional"]["id"]
            uuid = obj["_additional"]["id"]
            WEAVIATE_CLIENT.objects.delete(
              class_name: class_name,
              id: uuid
            )
            Rails.logger.info "Removed exercise from Weaviate for user #{exercise.user_id}, UUID: #{uuid}"
          end
        end
        return true
      else
        Rails.logger.info "No matching objects found in Weaviate for exercise ID #{exercise.id}"
        return false
      end
    rescue => e
      Rails.logger.error "Failed to remove exercise ID #{exercise.id} from Weaviate: #{e.message}"
      return false
    end
  end
end