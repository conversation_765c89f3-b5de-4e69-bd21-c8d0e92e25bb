require "anthropic"

class AnthropicLinkedinFeedbackService
  SYSTEM_PROMPT="Your response should be in 150 or fewer words. Don't rewrite the post, just provide suggestions. " +
    "Start your feedback by saying: Here's <PERSON><PERSON><PERSON><PERSON>'s feedback to strengthen your LinkedIn post:"

  def initialize
    @client = Anthropic::Client.new(access_token: ENV["ANTHROPIC_ACCESS_TOKEN"])
  end

  def call(story_content)
    prompt = "A strong LinkedIn post has the following elements: " +
      "(1) A strong hook to engage the reader in the first line, " +
      "(2) Valuable insights or lessons that benefit the reader, " +
      "(3) Personal authenticity that shows your unique voice, " +
      "(4) Clear formatting with short paragraphs and white space, and " +
      "(5) A simple call to action or thought-provoking question at the end. " +
      "Provide feedback on the following LinkedIn post draft: #{story_content}"

    generated_text = generate_text(prompt)

    Rails.logger.info "AnthropicLinkedinFeedbackService: #{generated_text}"

    # Sanitize the text to avoid JavaScript animation issues
    sanitized_text = sanitize_feedback(generated_text) if generated_text

    return sanitized_text
  end

  # Sanitize the feedback text to avoid JavaScript issues
  def sanitize_feedback(text)
    return nil if text.nil?

    # Replace escape sequences that cause problems with the typewriter animation
    sanitized = text.gsub(/\\"/,'"')  # Replace \" with simple "
                  .gsub(/\\'/,"'")    # Replace \' with simple '
                  .gsub(/\\n/,"\n")   # Replace \n with actual newlines
                  .gsub(/\\t/,"\t")   # Replace \t with actual tabs

    # Replace any other potential JavaScript-breaking characters as needed
    sanitized
  end

  private

  def generate_text(prompt, max_tokens_to_sample=1000)
    begin
      response = @client.messages(
        parameters: {
          model: "claude-3-7-sonnet-20250219",
          system: SYSTEM_PROMPT,
          messages: [
            {"role": "user", "content": prompt}
          ],
          temperature: 0,
          max_tokens: 1000
        }
      )
      return response["content"][0]["text"]
    rescue StandardError => e
      puts "Error generating text: #{e.message}"
      return nil
    end
  end

  def strip_brackets_and_newlines(input_string)
    # Remove "[" and "]" characters
    cleaned_string = input_string.gsub(/[\[\]]/, '')

    # Remove "\n" characters
    cleaned_string.gsub!("\n", '')

    return cleaned_string
  end
end