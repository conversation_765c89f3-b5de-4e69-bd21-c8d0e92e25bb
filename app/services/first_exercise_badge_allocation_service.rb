class FirstExerciseBadgeAllocationService
  def initialize(user)
    @user = user
  end

  def call
    @user.badges.create(
      name: "Imagination Awakening",
      description: "By completing your first exercise to hone your skills as a storyteller, you have taken the next step in unleashing your creativity.",
      icon: "Growth_Badge_Line.png"
    )

    UserMailer.first_exercise_badge(@user).deliver_later
  end
end