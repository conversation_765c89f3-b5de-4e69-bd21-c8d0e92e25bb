class EmailReminderService
  def call
    users_to_remind.each do |user|
      next unless user.premium_user?

      case user.practice_frequency
      when "daily"
        Rails.logger.info "Sending homework_for_life reminder to #{user.email}"
        ReminderMailers.h4l_mailer(user).deliver_later
      when "weekly"
        if Date.today.sunday?
          Rails.logger.info "Sending homework_for_life reminder to #{user.email}"
          ReminderMailers.h4l_mailer(user).deliver_later
        end
      when "never"
        Rails.logger.info "Not sending homework_for_life reminder to #{user.email}"
      else
        Rails.logger.info "Unknown practice frequency for #{user.email}"
      end
    end
  end

  private

  def users_to_remind
    case time_of_day
    when "morning"
      User.where(practice_time_of_day: "morning")
    when "afternoon"
      User.where(practice_time_of_day: "afternoon")
    when "evening"
      User.where(practice_time_of_day: "evening")
    else
      User.where(practice_time_of_day: "morning")
    end
  end

  def time_of_day
    current_hour = Time.now.hour

    case current_hour
    when 6..11
      "morning"
    when 12..17
      "afternoon"
    when 18..21
      "evening"
    else
      "night"
    end
  end
end