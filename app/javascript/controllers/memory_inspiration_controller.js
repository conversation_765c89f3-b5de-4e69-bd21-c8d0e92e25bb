import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="memory-inspiration"
export default class extends Controller {
  static targets = ["promptSelect", "selectedPrompt"]

  connect() {
    // Initialize the selected prompt with the initial value
    if (this.hasPromptSelectTarget && this.hasSelectedPromptTarget) {
      this.selectedPromptTarget.innerText = this.promptSelectTarget.value
    }
  }

  // Called when the prompt select changes
  change() {
    if (this.hasPromptSelectTarget && this.hasSelectedPromptTarget) {
      this.selectedPromptTarget.innerText = this.promptSelectTarget.value
    }
  }
}