import { Controller } from "@hotwired/stimulus"
import React from "react"
import { createRoot } from "react-dom/client"
import StoryGenerationLoading from "../components/StoryGenerationLoading"

// This controller handles showing the StoryGenerationLoading component
// when a "Craft a story from this memory" button is clicked
export default class extends Controller {
  connect() {
    this.setupStoryGenerationButtons()
  }

  setupStoryGenerationButtons() {
    // Find all buttons with the story-generation-btn class
    const buttons = document.querySelectorAll('.story-generation-btn')
    
    // Add click event listener to each button
    buttons.forEach(button => {
      button.addEventListener('click', this.handleStoryGenerationClick.bind(this))
    })
  }

  handleStoryGenerationClick(event) {
    // Prevent the default form submission
    event.preventDefault()
    
    // Get the form that contains the button
    const form = event.target.closest('form')
    
    // Show the loading component
    this.showLoadingComponent()
    
    // Submit the form programmatically
    setTimeout(() => {
      form.submit()
    }, 100)
  }

  showLoadingComponent() {
    // Find the container for the loading component
    const container = document.getElementById('story-generation-loading-container')
    
    if (container) {
      // Create a root for the React component
      const root = createRoot(container)
      
      // Render the StoryGenerationLoading component using React.createElement instead of JSX
      root.render(
        React.createElement(StoryGenerationLoading, { show: true })
      )
    }
  }
}