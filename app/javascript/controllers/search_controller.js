import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Clear notice when typing in search input
    const searchInput = document.getElementById('memory-search-input');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        const alertElement = document.querySelector('#search-results-container .alert');
        if (alertElement) {
          alertElement.remove();
        }
      });
    }
  }
}