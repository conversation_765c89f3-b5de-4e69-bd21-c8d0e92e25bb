import React from 'react';

const StoryPublishPanel = ({ storyId, isPublished, userId }) => {
  const publishPath = `/stories/${storyId}/publish`;
  const unpublishPath = `/stories/${storyId}/unpublish`;
  const previewPath = `/users/${userId}/stories#story-${storyId}`;

  return (
    <div className="card shadow-sm border-0 mb-4">
      <div className="card-body p-4">
        <div className="d-flex align-items-center justify-content-between">
          <div>
            <h5 className="mb-1">Share your story with readers</h5>
            <p className="text-secondary mb-0">
              {isPublished ? 
                "Your story is currently published and visible to others." : 
                "Make your story visible to other StoryCoach users."}
            </p>
          </div>
          <div>
            {isPublished ? (
              <>
                <a href={unpublishPath} 
                   data-method="patch" 
                   className="btn btn-outline-danger">
                  <i className="fa-solid fa-eye-slash me-2"></i>Unpublish
                </a>
                <a href={previewPath} 
                   className="btn btn-outline-primary ms-2" 
                   target="_blank" 
                   rel="noopener">
                  <i className="fa-solid fa-external-link me-2"></i>Preview
                </a>
              </>
            ) : (
              <a href={publishPath} 
                 data-method="patch" 
                 className="btn btn-success">
                <i className="fa-solid fa-globe me-2"></i>Publish Story
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoryPublishPanel;