import React, { useEffect, useState } from 'react';

const Confetti = ({ show }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (show) {
      // Load the confetti library dynamically
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js';
      script.async = true;
      document.body.appendChild(script);

      script.onload = () => {
        setVisible(true);
        
        // Run the confetti animation
        window.confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 }
        });
        
        // Hide the container after animation
        setTimeout(() => {
          setVisible(false);
        }, 3000);
      };
      
      return () => {
        document.body.removeChild(script);
      };
    }
  }, [show]);

  if (!show) return null;

  return (
    <div 
      style={{
        position: 'fixed', 
        top: 0, 
        left: 0, 
        width: '100%', 
        height: '100%', 
        pointerEvents: 'none', 
        zIndex: 9999,
        display: visible ? 'block' : 'none'
      }}
    />
  );
};

export default Confetti;