import React, { useEffect } from 'react';
import AiFeedbackLoading from './AiFeedbackLoading';
import AiFeedbackTypewriter from './AiFeedbackTypewriter';
import StoryPublishPanel from './StoryPublishPanel';
import Confetti from './Confetti';

const StoryPage = ({ 
  storyId, 
  isEditPage, 
  isPublished,
  userId,
  hasSuccess,
  feedbackContent,
  feedbackTimestamp,
  isNewFeedback
}) => {
  // Handle prompt selection for new stories
  useEffect(() => {
    const promptSelect = document.getElementById('prompt-select');
    const selectedPromptDiv = document.getElementById('selected-prompt');
    
    if (promptSelect && selectedPromptDiv) {
      promptSelect.addEventListener('change', function() {
        selectedPromptDiv.innerText = promptSelect.value;
      });
    }
  }, []);

  return (
    <>
      {/* Show confetti when success flash is present */}
      <Confetti show={hasSuccess} />
      
      {/* Render the loading component where it's needed */}
      {document.getElementById('ai-feedback-loading-container') && (
        <div id="ai-feedback-loading-root"></div>
      )}
      
      {/* Render the publish panel for edit page */}
      {isEditPage && (
        <div id="publish-panel-root"></div>
      )}
      
      {/* Render the AI feedback if available */}
      {feedbackContent && (
        <div id="ai-feedback-root"></div>
      )}
    </>
  );
};

// Initialize all React components
document.addEventListener('DOMContentLoaded', function() {
  // AI loading indicator
  const loadingContainer = document.getElementById('ai-feedback-loading-container');
  if (loadingContainer) {
    const root = createRoot(loadingContainer);
    root.render(<AiFeedbackLoading />);
  }
  
  // AI feedback display
  const feedbackContainer = document.getElementById('feedback-container');
  if (feedbackContainer) {
    const feedbackContent = feedbackContainer.getAttribute('data-feedback');
    const feedbackTimestamp = feedbackContainer.getAttribute('data-timestamp');
    const isNewFeedback = feedbackContainer.getAttribute('data-is-new') === 'true';
    
    if (feedbackContent) {
      const parent = feedbackContainer.parentNode;
      const root = createRoot(feedbackContainer);
      root.render(
        <AiFeedbackTypewriter 
          feedback={feedbackContent}
          timestamp={feedbackTimestamp}
          isNew={isNewFeedback}
        />
      );
    }
  }
  
  // Story publish panel
  const publishContainer = document.getElementById('publish-panel-container');
  if (publishContainer) {
    const storyId = publishContainer.getAttribute('data-story-id');
    const isPublished = publishContainer.getAttribute('data-published') === 'true';
    const userId = publishContainer.getAttribute('data-user-id');
    
    const root = createRoot(publishContainer);
    root.render(
      <StoryPublishPanel 
        storyId={storyId}
        isPublished={isPublished}
        userId={userId}
      />
    );
  }
  
  // Confetti for success message
  const hasSuccess = document.body.getAttribute('data-success') === 'true';
  const confettiContainer = document.getElementById('confetti-container');
  if (confettiContainer && hasSuccess) {
    const root = createRoot(confettiContainer);
    root.render(<Confetti show={true} />);
  }
});

export default StoryPage;