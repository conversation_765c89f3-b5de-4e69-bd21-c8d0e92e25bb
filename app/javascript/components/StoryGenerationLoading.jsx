import React, { useState, useEffect } from 'react';

const StoryGenerationLoading = ({ show }) => {
  const [dots, setDots] = useState('');

  // Animate the dots
  useEffect(() => {
    if (!show) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [show]);

  if (!show) return null;

  return (
    <div className="story-generation-overlay">
      <div className="story-generation-content">
        <div className="spinner-border text-primary mb-3" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <h4 className="mb-2">Crafting your story</h4>
        <p className="text-muted">
          AI is transforming your memory into a structured story{dots}
        </p>
        <p className="small text-muted mt-3">
          This may take a few moments. Please don't close this page.
        </p>
      </div>
    </div>
  );
};

export default StoryGenerationLoading;