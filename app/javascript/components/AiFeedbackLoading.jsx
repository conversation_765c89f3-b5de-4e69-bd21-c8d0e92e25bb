import React, { useState, useEffect } from 'react';

const AiFeedbackLoading = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleFormSubmit = (e) => {
      const checkbox = document.getElementById('ai_feedback_checkbox');
      if (checkbox && checkbox.checked) {
        setIsVisible(true);
      }
    };

    const form = document.getElementById('story-form');
    if (form) {
      form.addEventListener('submit', handleFormSubmit);
    }

    return () => {
      if (form) {
        form.removeEventListener('submit', handleFormSubmit);
      }
    };
  }, []);

  if (!isVisible) return null;

  return (
    <div className="mt-2">
      <div className="d-flex align-items-center text-primary">
        <div className="spinner-border spinner-border-sm me-2" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <span>Generating AI feedback for your story...</span>
      </div>
    </div>
  );
};

export default AiFeedbackLoading;