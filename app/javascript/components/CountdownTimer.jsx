import React, { Component } from 'react';

class CountdownTimer extends Component {
  constructor(props) {
    super(props);

    this.state = {
      timeRemaining: this.props.initialTime, // Initial time in seconds
    };

    this.intervalId = null;
  }

  componentDidMount() {
    this.startTimer();
  }

  componentWillUnmount() {
    this.clearTimer();
  }

  startTimer() {
    this.intervalId = setInterval(() => {
      if (this.state.timeRemaining > 0) {
        this.setState({ timeRemaining: this.state.timeRemaining - 1 });
      } else {
        this.clearTimer();
      }
    }, 1000);
  }

  clearTimer() {
    clearInterval(this.intervalId);
  }

  render() {
    const { timeRemaining } = this.state;

    return (
      <div>
        <p>Time Remaining: {timeRemaining} seconds</p>
      </div>
    );
  }
}

export default CountdownTimer;
