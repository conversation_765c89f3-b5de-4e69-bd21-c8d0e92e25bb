import React, { useState } from 'react';

const PromptSelector = ({ availablePrompts }) => {
  const [selectedPrompt, setSelectedPrompt] = useState(availablePrompts[0]);

  const handleSelectChange = (e) => {
    setSelectedPrompt(e.target.value);
  };

  return (
    <div className="input-group">
      <span className="input-group-text">Pick a prompt:</span>
      <select
        value={selectedPrompt}
        onChange={handleSelectChange}
        className="form-select"
      >
        {availablePrompts.map((prompt) => (
          <option key={prompt} value={prompt}>
            {prompt}
          </option>
        ))}
      </select>
      <div>
        <p>Selected prompt: {selectedPrompt}</p>
      </div>
    </div>
  );
};

export default PromptSelector;
