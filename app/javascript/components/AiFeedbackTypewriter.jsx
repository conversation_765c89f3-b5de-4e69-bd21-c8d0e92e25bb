import React, { useState, useEffect, useRef } from 'react';

const AiFeedbackTypewriter = ({ feedback, timestamp, isNew }) => {
  // State for managing the displayed text and animation
  const [displayedText, setDisplayedText] = useState('');
  const [isAnimating, setIsAnimating] = useState(true);
  const [showSkipNotice, setShowSkipNotice] = useState(false);

  // Refs for DOM elements
  const containerRef = useRef(null);

  // The text should already be sanitized by the server
  const originalText = feedback || '';

  // Simple typewriter animation using React state
  useEffect(() => {
    if (!originalText || !isAnimating) return;

    let currentIndex = 0;
    let typingTimer = null;

    // Function to add the next character
    const typeNextChar = () => {
      if (currentIndex >= originalText.length) {
        // We're done
        setIsAnimating(false);
        return;
      }

      // Add the next character
      setDisplayedText(originalText.substring(0, currentIndex + 1));
      currentIndex++;

      // Schedule the next character with a random delay for a more natural feel
      const delay = Math.floor(Math.random() * 15) + 20; // 20-35ms
      typingTimer = setTimeout(typeNextChar, delay);
    };

    // Start with a small delay
    const initialDelay = setTimeout(() => {
      typeNextChar();
    }, 600);

    // Cleanup function
    return () => {
      clearTimeout(initialDelay);
      clearTimeout(typingTimer);
    };
  }, [originalText, isAnimating]);

  // Format the text into paragraphs
  const formatText = () => {
    if (!displayedText) return null;

    // Split by double newlines to get paragraphs
    const paragraphs = displayedText.split(/\n\s*\n/);

    // Create paragraph elements
    return paragraphs.map((paragraph, index) => {
      const isNumbered = /^\s*\d+\.\s/.test(paragraph);

      // Apply different styling for numbered items
      const className = isNumbered ? "numbered-paragraph" : "";

      return (
        <p key={index} className={className}>{paragraph}</p>
      );
    });
  };

  // Skip animation handler
  const handleSkip = () => {
    setIsAnimating(false);
    setDisplayedText(originalText);
    setShowSkipNotice(true);

    // Hide the skip notice after a delay
    setTimeout(() => {
      setShowSkipNotice(false);
    }, 2000);
  };

  // Highlight and scroll effect for new feedback
  useEffect(() => {
    if (isNew && containerRef.current) {
      // Add highlight
      containerRef.current.style.boxShadow = '0 0 0 3px rgba(13, 110, 253, 0.5)';

      // Scroll to this element
      setTimeout(() => {
        containerRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Remove highlight after animation completes
        setTimeout(() => {
          if (containerRef.current) {
            containerRef.current.style.transition = 'box-shadow 0.5s ease';
            containerRef.current.style.boxShadow = '';
          }
        }, 4000);
      }, 500);
    }
  }, [isNew]);

  // No content, no render
  if (!originalText) return null;

  return (
    <div ref={containerRef} className="card shadow-sm border-0">
      <div className="card-header bg-primary text-white py-3">
        <h5 className="mb-0 text-white">
          <i className="fa-solid fa-robot me-2"></i>AI-Powered Feedback
        </h5>
      </div>
      <div className="card-body p-4">
        <div className="feedback-content">
          <div className="mb-0 position-relative" style={{ minHeight: '200px' }}>
            {formatText()}

            {/* Blinking cursor during animation */}
            {isAnimating && <span className="typing-cursor">|</span>}

            {/* Skip button during animation */}
            {isAnimating && (
              <button
                onClick={handleSkip}
                className="btn btn-sm btn-light"
                style={{
                  position: 'absolute',
                  bottom: '-10px',
                  right: '0',
                  fontSize: '0.75rem',
                  padding: '0.25rem 0.5rem'
                }}
              >
                <i className="fa-solid fa-forward-step me-1"></i>
                Skip
              </button>
            )}

            {/* Notice shown after skipping */}
            {showSkipNotice && (
              <div className="text-muted text-center mt-3 small">
                <i className="fa-solid fa-circle-info me-1"></i> Animation skipped
              </div>
            )}
          </div>

          <hr className="my-4" />

          <div className="feedback-meta text-muted d-flex justify-content-between align-items-center">
            <small>Generated by Claude</small>
            <small>{timestamp}</small>
          </div>
        </div>
      </div>

      <style>
        {`
          .typing-cursor {
            font-weight: 100;
            color: #666;
            animation: blink 1s infinite;
            display: inline-block;
            margin-left: 2px;
          }

          @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
          }

          .feedback-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .numbered-paragraph {
            padding-left: 1rem;
            text-indent: -1rem;
          }
        `}
      </style>
    </div>
  );
};

export default AiFeedbackTypewriter;