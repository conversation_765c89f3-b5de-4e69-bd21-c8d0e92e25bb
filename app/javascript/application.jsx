// This file is automatically compiled by Webpack, along with any other files
// present in this directory. You're encouraged to place your actual application logic in
// a relevant structure within app/javascript and only use these pack files to reference
// that code so it'll be compiled.

import "@hotwired/turbo-rails"
require("@rails/activestorage").start()
//require("trix")
//require("@rails/actiontext")
require("local-time").start()
require("@rails/ujs").start()

import './channels/**/*_channel.js'
import "./controllers/index.js"
import React from 'react';
import { createRoot } from 'react-dom/client';
import LogRocket from 'logrocket';

import CountdownTimer from './components/CountdownTimer.jsx';
import AiFeedbackLoading from './components/AiFeedbackLoading';
import AiFeedbackTypewriter from './components/AiFeedbackTypewriter';
import StoryPublishPanel from './components/StoryPublishPanel';
import Confetti from './components/Confetti';
import StoryGenerationLoading from './components/StoryGenerationLoading';

import * as bootstrap from "bootstrap"

document.addEventListener("turbo:load", () => {
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
  })

  var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
  var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl)
  })
})

document.addEventListener('DOMContentLoaded', function () {
  // Initialize countdown timer
  const initialTime = 600; // Initial time in seconds
  const timerContainer = document.getElementById('countdown-timer');
  if (timerContainer) {
    const root = createRoot(timerContainer);
    root.render(<CountdownTimer initialTime={initialTime} />);
  }

  // Initialize story components
  initializeStoryComponents();
});

// For Turbo navigation
document.addEventListener('turbo:load', initializeStoryComponents);
document.addEventListener('turbolinks:load', initializeStoryComponents);

// Create and show the story generation loading overlay
function showStoryGenerationLoading() {
  // Check if the overlay already exists
  let loadingOverlay = document.getElementById('story-generation-loading-container');

  // If it doesn't exist, create it
  if (!loadingOverlay) {
    loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'story-generation-loading-container';
    document.body.appendChild(loadingOverlay);

    const root = createRoot(loadingOverlay);
    root.render(<StoryGenerationLoading show={true} />);
  }

  return true; // Allow the link to proceed
}

// Add event listeners to "Craft a story from this memory" buttons
function initializeStoryGenerationButtons() {
  const craftStoryButtons = document.querySelectorAll('a[href*="memory"][href*="draft_story=true"]');

  craftStoryButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      // Show the loading overlay
      showStoryGenerationLoading();
    });
  });
}

function initializeStoryComponents() {
  // Initialize story generation buttons
  initializeStoryGenerationButtons();
  // AI loading indicator
  const loadingContainer = document.getElementById('ai-feedback-loading-container');
  if (loadingContainer && !loadingContainer._reactRootContainer) {
    try {
      const root = createRoot(loadingContainer);
      root.render(<AiFeedbackLoading />);
    } catch (e) {
      console.error('Error rendering AiFeedbackLoading component:', e);
    }
  }

  // AI feedback display
  const feedbackContainer = document.getElementById('feedback-container');
  if (feedbackContainer && !feedbackContainer._reactRootContainer) {
    try {
      const feedbackContent = feedbackContainer.getAttribute('data-feedback');
      const feedbackTimestamp = feedbackContainer.getAttribute('data-timestamp');
      const isNewFeedback = feedbackContainer.getAttribute('data-is-new') === 'true';

      if (feedbackContent) {
        const root = createRoot(feedbackContainer);
        root.render(
          <AiFeedbackTypewriter
            feedback={feedbackContent}
            timestamp={feedbackTimestamp}
            isNew={isNewFeedback}
          />
        );
      }
    } catch (e) {
      console.error('Error rendering AiFeedbackTypewriter component:', e);
    }
  }

  // Story publish panel
  const publishContainer = document.getElementById('publish-panel-container');
  if (publishContainer && !publishContainer._reactRootContainer) {
    try {
      const storyId = publishContainer.getAttribute('data-story-id');
      const isPublished = publishContainer.getAttribute('data-published') === 'true';
      const userId = publishContainer.getAttribute('data-user-id');

      if (storyId) {
        const root = createRoot(publishContainer);
        root.render(
          <StoryPublishPanel
            storyId={storyId}
            isPublished={isPublished}
            userId={userId}
          />
        );
      }
    } catch (e) {
      console.error('Error rendering StoryPublishPanel component:', e);
    }
  }

  // Confetti for success message
  const confettiContainer = document.getElementById('confetti-container');
  if (confettiContainer && !confettiContainer._reactRootContainer) {
    try {
      const hasSuccess = document.body.getAttribute('data-success') === 'true';
      if (hasSuccess) {
        const root = createRoot(confettiContainer);
        root.render(<Confetti show={true} />);
      }
    } catch (e) {
      console.error('Error rendering Confetti component:', e);
    }
  }
}

// Initialize LogRocket
LogRocket.init('fxzvgn/storycoachapp');

// Identify users for LogRocket
if (typeof gon !== 'undefined' && gon.current_user) {
  LogRocket.identify(gon.current_user.id, {
    name: gon.current_user.name,
    email: gon.current_user.email,
  });
}

// Integrate LogRocket with Google Analytics
if (typeof ga !== 'undefined') {
  LogRocket.getSessionURL(function (sessionURL) {
    ga('send', {
      hitType: 'event',
      eventCategory: 'LogRocket',
      eventAction: sessionURL,
    });
  });
}