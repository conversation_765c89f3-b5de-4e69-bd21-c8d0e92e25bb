<!DOCTYPE html>
<html class="h-100" lang="en">
  <head>
    <%= favicon_link_tag asset_path("favicon.ico") %>

    <%= canonical_tag -%>
    <link rel="alternate" hreflang="en" href="<%= request.original_url %>" />
    <link rel="alternate" hreflang="x-default" href="<%= request.original_url %>" />

    <% if Rails.env.production? %>
      <%= render "shared/google_tag_manager" %>
    <% end %>

    <%= display_meta_tags site: "StoryCoach.app" %>

    <%= render "shared/head" %>

    <%= include_gon %>

    <%= render "shared/crisp_chat" %>
  </head>

  <body class="d-flex flex-column h-100" <%= yield :body_attributes %>>
    <main class="flex-shrink-0">
      <%= render "shared/navbar" %>

      <div class="mt-4 mx-auto min-vh-100">
        <%= render "shared/notices" %>
        <%= yield %>
      </div>
    </main>

    <% unless current_user.present? %>
      <%= render 'shared/footer' %>
    <% end %>
  </body>
</html>
