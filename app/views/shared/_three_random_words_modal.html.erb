<div class="modal fade" id="threeRandomWordsModal" tabindex="-1" aria-labelledby="threeRandomWordsModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Exercise: Three Random Words</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>
          One of the most fun ways to trigger storyworthy memories is to play with random words that capture stories from the past.
        </p>
        <p>
          You get a set of 3 random words (e.g. apple, turtle, silver) that trigger a memory and allow you to deposit a "storyworthy"
          moment into your Memory Bank. Rather than cast a wide net (e.g. topic of love), this game is designed to be very specific.
        </p>
        <p>
          <%= image_tag "landing_page/three_random_words.png", class: "d-block mx-lg-auto img-fluid", style: "width: 500px;", alt: "Three Random Words exercise", loading:"lazy" %>
        </p>
        <p>
          There is no limit to how often you can practice. And if you don't like any of the words, just refresh the page to get new words.
        </p>
      </div>
    </div>
  </div>
</div>