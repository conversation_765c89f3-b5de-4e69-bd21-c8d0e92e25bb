<!-- Points and Levels Modal -->
<div class="modal fade" id="pointsAndLevelsModal" tabindex="-1" aria-labelledby="pointsAndLevelsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content border-0 shadow">
      <!-- Modal Header with gradient background -->
      <div class="modal-header bg-gradient-success text-white border-0">
        <h5 class="modal-title fw-bold" id="pointsAndLevelsModalLabel">
          <i class="fa-solid fa-trophy me-2"></i>Points & Levels
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body p-4">
        <% if current_user %>
          <!-- Current Status -->
          <div class="level-status-card mb-4">
            <div class="row g-0">
              <div class="col-6 p-3 text-center border-end">
                <div class="status-item">
                  <div class="small text-muted mb-1">Current Level</div>
                  <div class="d-flex align-items-center justify-content-center">
                    <span class="current-level"><%= current_user.level %></span>
                    <i class="fa-solid fa-arrow-right mx-2 text-success"></i>
                    <span class="next-level"><%= current_user.level + 1 %></span>
                  </div>
                </div>
              </div>
              <div class="col-6 p-3 text-center">
                <div class="status-item">
                  <div class="small text-muted mb-1">Points to Level Up</div>
                  <div class="points-display">
                    <%= @points_to_level_up %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="px-3 pb-3">
              <%
                # Calculate progress percentage with error handling
                begin
                  current_level_points = @levels_and_points_map[current_user.level][:points]
                  next_level_points = @levels_and_points_map[current_user.level + 1][:points]
                  points_needed = next_level_points - current_level_points
                  points_needed = 1 if points_needed <= 0  # Avoid division by zero
                  user_progress = points_needed - @points_to_level_up
                  progress_percentage = [(user_progress.to_f / points_needed) * 100, 100].min
                rescue => e
                  # Default to 0% if there's an error
                  progress_percentage = 0
                end
              %>
              <div class="level-progress">
                <div class="progress" style="height: 10px;">
                  <div class="progress-bar bg-success" role="progressbar" style="width: <%= progress_percentage %>%;"
                      aria-valuenow="<%= progress_percentage %>" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="d-flex justify-content-between mt-1">
                  <small class="text-muted">Level <%= current_user.level %></small>
                  <small class="text-muted">Level <%= current_user.level + 1 %></small>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- How to Earn Points -->
        <div class="card border-0 bg-light mb-4">
          <div class="card-body">
            <h6 class="card-title d-flex align-items-center mb-3">
              <i class="fa-solid fa-star text-warning me-2"></i>How to Earn Points
            </h6>

            <div class="earning-options">
              <div class="option-item d-flex align-items-center mb-3">
                <div class="option-icon me-3">
                  <div class="icon-bg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center">
                    <i class="fa-solid fa-dumbbell text-primary"></i>
                  </div>
                </div>
                <div class="option-details flex-grow-1">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Complete Exercises</h6>
                    <span class="badge bg-primary">+1 point</span>
                  </div>
                  <small class="text-muted">Each writing exercise you finish earns you points</small>
                </div>
              </div>

              <div class="option-item d-flex align-items-center">
                <div class="option-icon me-3">
                  <div class="icon-bg bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center">
                    <i class="fa-solid fa-book text-success"></i>
                  </div>
                </div>
                <div class="option-details flex-grow-1">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Write Stories</h6>
                    <span class="badge bg-success">+3 points</span>
                  </div>
                  <small class="text-muted">Draft a new story to earn more points</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Level Requirements -->
        <div class="card border-0 bg-light">
          <div class="card-body">
            <h6 class="card-title d-flex align-items-center mb-3">
              <i class="fa-solid fa-medal text-warning me-2"></i>Level Requirements
            </h6>

            <div class="level-requirements">
              <div class="table-responsive">
                <table class="table table-borderless mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Level</th>
                      <th class="text-end">Points Required</th>
                      <th class="text-end">AI Credits</th>
                    </tr>
                  </thead>
                  <tbody>
                    <%
                      # Different structures for logged-in vs logged-out views
                      if defined?(@levels_and_points_map) && @levels_and_points_map.present? && current_user
                        # For logged-in users with the proper levels map structure
                        @levels_and_points_map.each_with_index do |level_data, level|
                          next if level == 0  # Skip level 0
                    %>
                          <tr class="<%= level == current_user.level ? 'table-primary' : '' %>">
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="level-badge me-2 <%= level == current_user.level ? 'current' : '' %>">
                                  <%= level %>
                                </div>
                                <% if level == current_user.level %>
                                  <span class="badge bg-primary rounded-pill">Current</span>
                                <% end %>
                              </div>
                            </td>
                            <td class="text-end"><%= level_data[:points] %></td>
                            <td class="text-end">
                              <span class="badge bg-info rounded-pill">
                                <i class="fa-solid fa-robot me-1"></i><%= level_data[:ai_credits] || level %>
                              </span>
                            </td>
                          </tr>
                    <%
                        end
                      else
                        # Default data for logged-out users
                        default_levels = {
                          1 => { points: 0, ai_credits: 1 },
                          2 => { points: 10, ai_credits: 2 },
                          3 => { points: 25, ai_credits: 3 },
                          4 => { points: 45, ai_credits: 4 },
                          5 => { points: 70, ai_credits: 5 }
                        }
                        
                        default_levels.each do |level, data|
                    %>
                          <tr>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="level-badge me-2">
                                  <%= level %>
                                </div>
                              </div>
                            </td>
                            <td class="text-end"><%= data[:points] %></td>
                            <td class="text-end">
                              <span class="badge bg-info rounded-pill">
                                <i class="fa-solid fa-robot me-1"></i><%= data[:ai_credits] %>
                              </span>
                            </td>
                          </tr>
                    <%
                        end
                      end
                    %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <% unless current_user %>
          <!-- CTA for logged-out users -->
          <div class="text-center mt-4">
            <p class="text-muted mb-3">Ready to start earning points and improving your storytelling skills?</p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
              <%= link_to new_user_registration_path, class: "btn btn-primary" do %>
                <i class="fa-solid fa-user-plus me-1"></i>Sign Up
              <% end %>
              <%= link_to new_session_path(:user), class: "btn btn-outline-secondary" do %>
                <i class="fa-solid fa-sign-in-alt me-1"></i>Log In
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-gradient-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
  }

  .level-status-card {
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: #fff;
  }

  .current-level, .next-level {
    font-size: 1.75rem;
    font-weight: bold;
  }

  .current-level {
    color: #1cc88a;
  }

  .next-level {
    color: #4e73df;
  }

  .points-display {
    font-size: 1.75rem;
    font-weight: bold;
    color: #f6c23e;
  }

  .icon-bg {
    width: 40px;
    height: 40px;
  }

  .earning-options .option-item:hover .icon-bg,
  .level-requirements .level-badge:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }

  .level-badge {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }

  .level-badge.current {
    background-color: #4e73df;
    color: white;
  }
</style>