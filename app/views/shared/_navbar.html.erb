<% if current_user != true_user %>
  <div class="alert alert-warning text-center">
    You're logged in as <b><%= current_user.name %> (<%= current_user.email %>)</b>
    <%= link_to stop_impersonating_madmin_impersonates_path, method: :post do %><%= icon("fas", "times") %> Logout <% end %>
  </div>
<% end %>

<nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm sticky-top">
  <div class="container">
    <!-- App logo/name for all users -->
    <% if !user_signed_in? %>
      <%= link_to root_path, class: "navbar-brand mx-auto d-md-none" do %>
        <span class="fw-bold text-primary"><%= Rails.configuration.application_name %></span>
      <% end %>
    <% end %>

    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarMain">
      <% if user_signed_in? %>
        <!-- Mobile menu items for logged in users -->
        <ul class="navbar-nav mt-2 mt-md-0 d-md-none">
          <li class="nav-item">
            <%= link_to user_path(current_user), class: "nav-link py-2" do %>
              <i class="fa fa-home me-2 text-primary"></i> Dashboard
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to exercises_path, class: "nav-link py-2" do %>
              <i class="fa fa-search me-2 text-primary"></i> Find Stories
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to stories_path, class: "nav-link py-2" do %>
              <i class="fa fa-pen-fancy me-2 text-primary"></i> Craft Stories
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to edit_user_registration_path, class: "nav-link py-2" do %>
              <i class="fa fa-cog me-2 text-primary"></i> Settings
            <% end %>
          </li>
          <% if current_user.admin? && respond_to?(:madmin_root_path) %>
            <li class="nav-item">
              <%= link_to madmin_root_path, class: "nav-link py-2" do %>
                <i class="fa fa-lock me-2 text-primary"></i> Admin Area
              <% end %>
            </li>
          <% end %>
          <li class="nav-item">
            <%= button_to destroy_user_session_path, method: :delete, class: "nav-link w-100 text-start border-0 bg-transparent py-2" do %>
              <i class="fa fa-sign-out-alt me-2 text-primary"></i> Logout
            <% end %>
          </li>
        </ul>

        <!-- Desktop menu centered for logged in users -->
        <ul class="navbar-nav mx-auto d-none d-md-flex">
          <li class="nav-item">
            <%= link_to user_path(current_user), class: "nav-link" do %>
              <div class="navbar-icon d-flex">
                <i class="fa fa-home" aria-hidden="true"></i>
              </div>
              <span class="navbar-icon-text">Dashboard</span>
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to exercises_path, class: "nav-link" do %>
              <div class="navbar-icon d-flex">
                <i class="fa fa-search" aria-hidden="true"></i>
              </div>
              <span class="navbar-icon-text">Find Stories</span>
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to stories_path, class: "nav-link" do %>
              <div class="navbar-icon d-flex">
                <i class="fa fa-pen-fancy" aria-hidden="true"></i>
              </div>
              <span class="navbar-icon-text">Craft Stories</span>
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to edit_user_registration_path, class: "nav-link" do %>
              <div class="navbar-icon d-flex">
                <i class="fa fa-cog" aria-hidden="true"></i>
              </div>
              <span class="navbar-icon-text">Settings</span>
            <% end %>
          </li>
          
          <li class="nav-item">
            <%= button_to destroy_user_session_path, method: :delete, class: "nav-link border-0 bg-transparent" do %>
              <div class="navbar-icon d-flex">
                <i class="fa fa-sign-out-alt" aria-hidden="true"></i>
              </div>
              <span class="navbar-icon-text">Logout</span>
            <% end %>
          </li>
        </ul>
      <% else %>
        <!-- Brand name centered for non-logged in users (desktop) -->
        <div class="navbar-nav mx-auto d-none d-md-block">
          <%= link_to root_path, class: "navbar-brand" do %>
            <span class="fw-bold text-primary"><%= Rails.configuration.application_name %></span>
          <% end %>
        </div>

        <!-- Auth links right-aligned for non-logged in users -->
        <ul class="navbar-nav d-flex ms-auto">
          <li class="nav-item">
            <%= link_to new_user_registration_path, class: "nav-link auth-link mx-1" do %>
              <i class="fa fa-user-plus me-1 d-none d-sm-inline-block"></i> Sign Up
            <% end %>
          </li>
          <li class="nav-item">
            <%= link_to new_user_session_path, class: "btn btn-primary btn-sm auth-button mx-1" do %>
              <i class="fa fa-sign-in-alt me-1"></i> Login
            <% end %>
          </li>
        </ul>
      <% end %>
    </div>
  </div>
</nav>

<style>
  .auth-link {
    padding: 0.375rem 0.75rem;
    display: inline-flex;
    align-items: center;
  }
  
  .auth-button {
    padding: 0.375rem 0.75rem;
    margin-top: 0.125rem;
    display: inline-flex;
    align-items: center;
  }
  
  @media (max-width: 767.98px) {
    .auth-link, .auth-button {
      display: block;
      width: 100%;
      text-align: left;
      margin: 0.25rem 0;
    }
    
    .navbar-nav {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }
</style>