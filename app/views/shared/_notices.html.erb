<% flash.each do |msg_type, message| %>
  <% next if msg_type.to_s == 'new_feedback' %>
  <div class="container mt-4 mx-auto">
    <div class="alert <%= bootstrap_class_for(msg_type) %> alert-dismissible fade show shadow-sm" role="alert">
      <% if msg_type == "success" %>
        <div class="d-flex align-items-center">
          <div class="me-3">
            <i class="fa-solid fa-circle-check fs-4 text-success"></i>
          </div>
          <div>
            <%= message %>
          </div>
        </div>
      <% else %>
        <%= message %>
      <% end %>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  </div>
<% end %>
