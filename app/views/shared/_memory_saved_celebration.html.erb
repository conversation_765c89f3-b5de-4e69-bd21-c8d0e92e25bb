<% if flash[:memory_saved] %>
  <div id="memory-saved-celebration" class="memory-celebration-overlay">
    <div class="celebration-content">
      <div class="celebration-icon">
        <i class="fa fa-check-circle"></i>
      </div>
      <h3>Memory Saved!</h3>
      <p><%= flash[:notice] %></p>
      <div class="celebration-points">
        <span>+1 point</span>
      </div>
      <button type="button" class="btn btn-light mt-3" onclick="document.getElementById('memory-saved-celebration').style.display='none';">Continue</button>
    </div>
  </div>

  <script>
    // Auto-dismiss after 3 seconds
    setTimeout(function() {
      const celebrationEl = document.getElementById('memory-saved-celebration');
      if (celebrationEl) {
        celebrationEl.classList.add('fade-out');
        setTimeout(function() {
          celebrationEl.style.display = 'none';
        }, 1000);
      }
    }, 3000);
  </script>
<% end %>