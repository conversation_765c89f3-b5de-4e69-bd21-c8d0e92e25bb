
<div class="container mt-5 mb-5">
  <div class="row">
    <div class="col-md-8 mb-4">
      <h1 class="display-5 fw-bold mb-2">Capture a Storyworthy Memory</h1>
      <p class="lead text-secondary">
        Choose a prompt that sparks a memory, then save it for future storytelling
      </p>
    </div>
  </div>

  <div class="row flex-lg-row-reverse g-4">
    <div class="col-md-4">
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-header bg-light py-3">
          <h5 class="mb-0">
            <i class="fa-solid fa-lightbulb text-warning me-2"></i>Memory Capture Tips
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-primary bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-clock text-primary"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">1-1 Rule</h6>
                <p class="text-secondary mb-0 small">One minute to think of a memory and one minute to capture it.</p>
              </div>
            </div>

            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-success bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-arrows-rotate text-success"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">Try Different Prompts</h6>
                <p class="text-secondary mb-0 small">Can't think of a memory? Switch to a different prompt.</p>
              </div>
            </div>

            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-warning bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-calendar-check text-warning"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">Practice Regularly</h6>
                <p class="text-secondary mb-0 small">The more memories you capture, the more stories you'll have to share.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <div class="card shadow-sm border-0">
        <div class="card-body p-4">
          <%= form_for @exercise, url: memory_inspiration_exercises_path, method: :post, html: { class: "memory-inspiration-form", data: { controller: "memory-inspiration" } } do |f| %>
            <div class="mb-4">
              <label class="form-label fw-medium mb-2">Choose a memory prompt:</label>
              <div class="input-group">
                <%= f.select :prompt,
                  @available_prompts,
                  { selected: @available_prompts.first},
                  { class: "form-select",
                    id: "prompt-select",
                    data: {
                      memory_inspiration_target: "promptSelect",
                      action: "change->memory-inspiration#change"
                    }
                  }
                %>
              </div>
            </div>

            <div class="card bg-light border-0 mb-4">
              <div class="card-body p-3">
                <h6 class="text-secondary mb-2 small text-uppercase">Your Selected Prompt:</h6>
                <div id="selected-prompt" class="fs-5 fw-medium text-primary" data-memory-inspiration-target="selectedPrompt">
                  <%= @available_prompts.first %>
                </div>
              </div>
            </div>

            <div class="mb-4">
              <label class="form-label fw-medium mb-2">Your memory:</label>
              <%= f.text_area :note,
                autofocus: true,
                placeholder: "Describe your memory based on the prompt above...",
                class: "form-control",
                required: true,
                style: "height: 240px; border-radius: 0.5rem;text-indent: 0;"
              %>
            </div>

            <div class="d-grid">
              <%= f.submit "Save Your Memory", class: "btn btn-primary py-2 fs-5" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>