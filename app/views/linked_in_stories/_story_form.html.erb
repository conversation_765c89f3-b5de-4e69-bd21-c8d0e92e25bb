<div class="card shadow-sm border-0">
  <div class="card-body p-4">
    <%= form_for story, url: url, method: method, html: { class: "story-form", id: "story-form" } do |f| %>
      <div class="mb-4">
        <%= f.text_area :content,
            placeholder: "Write a LinkedIn post that stands out. \n\nWill this hook make someone stop and click see more? \nWould I want to click see more if I saw this? \nIs it formatted for easy reading?",
            class: 'form-control',
            required: true,
            style: "height: #{local_assigns[:content_height] || '500px'}; border-radius: 0.5rem;text-indent: 0;",
            autofocus: false
        %>
      </div>

      <% if current_user.has_ai_credits? %>
        <div class="mb-4">
          <div class="form-check">
            <%= f.check_box :ai_feedback, class: "form-check-input", id: "ai_feedback_checkbox" %>
            <%= f.label :ai_feedback, "Provide AI-powered feedback to help me improve this LinkedIn post", class: "form-check-label" %>
            <small class="d-block text-muted mt-1">
              You have <%= current_user.ai_feedback_credits %> AI feedback credits remaining
              <span data-bs-toggle="modal" data-bs-target="#aiCreditsModal" style="cursor: pointer;"><i class="fa fa-info-circle fa-xs"></i></span>
            </small>
          </div>
          <div id="ai-feedback-loading-container"></div>
        </div>
      <% end %>

      <div class="d-grid">
        <%= f.submit submit_text, class: "btn btn-primary py-2 fs-5" %>
      </div>
    <% end %>
  </div>
</div>

<div id="feedback-container" data-feedback="<%= @latest_feedback&.content %>" data-timestamp="<%= @latest_feedback&.created_at&.strftime("%b %d, %Y") %>" data-is-new="<%= !!flash[:new_feedback] %>"></div>