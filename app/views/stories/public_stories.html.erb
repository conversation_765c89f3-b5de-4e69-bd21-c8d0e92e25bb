<div class="container mt-4 mb-5">
  <div class="row">
    <!-- Left sidebar with user profile -->
    <div class="col-lg-4 mb-4">
      <!-- User Profile Card -->
      <div class="card user-profile-card shadow-sm border-0 mb-4">
        <!-- Profile Header with Background and Avatar -->
        <div class="bg-primary text-white p-4 rounded-top d-flex align-items-center">
          <div class="me-3">
            <div class="rounded-circle bg-white p-1" style="width: 70px; height: 70px; overflow: hidden;">
              <%= image_tag avatar_path(@user), class: "w-100 h-100 rounded-circle" %>
            </div>
          </div>
          <div>
            <h3 class="mb-0 fw-bold"><%= @user.name %></h3>
            <p class="mb-0 text-white-50">
              <i class="fa-solid fa-calendar-alt me-1"></i> Storyteller since <%= @user.created_at.strftime("%B %d, %Y") %>
            </p>
          </div>
        </div>

        <!-- Profile Content -->
        <div class="card-body p-4">
          <!-- Stories List -->
          <h5 class="card-title mb-3">
            <i class="fa-solid fa-book-open text-primary me-2"></i><%= @user.first_name %>'s Stories
          </h5>
          <div class="list-group list-group-flush mb-4">
            <% @stories.each_with_index do |story, index| %>
              <% story_title = story.introduction? ? "Introduction story" : (story.origin? ? "Origin story" : story.title) %>
              <%= link_to story_path(story), class: "list-group-item list-group-item-action border-0 px-0 py-2 d-flex align-items-center" do %>
                <div class="me-3 text-center" style="width: 30px;">
                  <% icon_class = story.introduction? ? "fa-solid fa-id-card" : (story.origin? ? "fa-solid fa-seedling" : "fa-solid fa-book") %>
                  <i class="<%= icon_class %> text-primary"></i>
                </div>
                <div class="text-truncate">
                  <%= story_title %>
                </div>
                <div class="ms-auto">
                  <small class="text-muted"><%= time_ago_in_words(story.created_at) %> ago</small>
                </div>
              <% end %>
            <% end %>
          </div>

          <!-- Share links -->
          <div class="d-grid">
            <a href="https://twitter.com/intent/tweet?url=<%= CGI.escape(request.original_url) %>&text=Check out <%= @user.name %>'s stories on StoryCoach"
               class="btn btn-outline-primary btn-sm">
              <i class="fa-solid fa-share-alt me-2"></i>Share this collection
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content with stories -->
    <div class="col-lg-8">
      <!-- Page header -->
      <div class="d-flex align-items-center justify-content-between mb-4">
        <h2 class="mb-0"><%= @user.name %>'s Stories</h2>

        <div class="badge bg-primary rounded-pill px-3 py-2">
          <i class="fa-solid fa-book me-1"></i> <%= pluralize(@stories.size, 'Story') %>
        </div>
      </div>

      <!-- Stories Cards -->
      <% if @stories.empty? %>
        <div class="alert alert-info">
          <i class="fa-solid fa-info-circle me-2"></i> This user hasn't published any stories yet.
        </div>
      <% else %>
        <% @stories.each_with_index do |story, index| %>
          <div id="story-<%= story.id %>" class="card shadow-sm mb-4 story-card border-0">
            <!-- Story Card Header -->
            <div class="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center py-3">
              <div class="d-flex align-items-center">
                <%
                  if story.introduction?
                    badge_class = "bg-info"
                    badge_text = "Introduction"
                    icon_class = "fa-solid fa-id-card"
                  elsif story.origin?
                    badge_class = "bg-success"
                    badge_text = "Origin Story"
                    icon_class = "fa-solid fa-seedling"
                  else
                    badge_class = "bg-primary"
                    badge_text = "Story"
                    icon_class = "fa-solid fa-book"
                  end
                %>
                <span class="badge <%= badge_class %> me-2">
                  <i class="<%= icon_class %> me-1"></i><%= badge_text %>
                </span>
                <h5 class="mb-0 ms-2 story-title">
                  <% if story.introduction? %>
                    Introduction story
                  <% elsif story.origin? %>
                    Origin story
                  <% else %>
                    <%= story.title %>
                  <% end %>
                </h5>
              </div>
              <small class="text-muted">
                <i class="fa-regular fa-calendar me-1"></i><%= story.created_at.strftime("%b %d, %Y") %>
              </small>
            </div>

            <!-- Story Card Body -->
            <div class="card-body p-4">
              <div class="story-content">
                <p class="card-text">
                  <%= truncate(story.content, length: 300) %>
                </p>
              </div>

              <!-- Read more link -->
              <div class="d-flex justify-content-end mt-3">
                <%= link_to story_path(story), class: "btn btn-primary" do %>
                  <i class="fa-solid fa-book-open me-2"></i>Read full story
                <% end %>
              </div>
            </div>

            <!-- Stats Footer -->
            <div class="card-footer bg-light py-3">
              <div class="d-flex justify-content-between align-items-center">
                <div class="metadata">
                  <small>
                    <i class="fa-solid fa-clock me-1"></i><%= distance_of_time_in_words(story.created_at, Time.now) %> ago
                  </small>
                </div>
                <div class="d-flex align-items-center">
                  <!-- Placeholder for interaction stats if you add them later -->
                  <div class="d-flex align-items-center me-3">
                    <i class="fa-regular fa-eye text-muted me-1"></i>
                    <small class="text-muted">Story</small>
                  </div>
                  <div class="d-flex align-items-center">
                    <i class="fa-regular fa-calendar text-muted me-1"></i>
                    <small class="text-muted"><%= story.created_at.strftime("%b %d") %></small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>

      <!-- Pagination -->
      <% if @pagy.pages > 1 %>
        <div class="mt-4">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <%== pagy_info(@pagy) %>
                </div>
                <div>
                  <%== pagy_bootstrap_nav(@pagy) %>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<style>
  .story-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .story-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
  }

  .story-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 300px;
  }

  .story-content {
    line-height: 1.6;
  }

  .user-profile-card {
    border-radius: 0.5rem;
    overflow: hidden;
  }
</style>