<div class="card shadow-sm border-0">
  <div class="card-body p-4">
    <%= form_for story, url: url, method: method, html: { class: "story-form", id: "story-form" } do |f| %>
      <% if show_prompt && params[:content].nil? && params[:memory].nil? %>
        <div class="mb-4">
          <label class="form-label fw-medium mb-2">Need a topic to write about? Select a prompt:</label>
          <div class="input-group">
            <%= f.select :prompt,
              @available_prompts,
              { selected: @available_prompts.first},
              { class: "form-select", id: "prompt-select" }
            %>
          </div>
        </div>

        <div class="card bg-light border-0 mb-4">
          <div class="card-body p-3">
            <h6 class="text-secondary mb-2 small text-uppercase">Your Selected Prompt:</h6>
            <div id="selected-prompt" class="fs-5 fw-medium text-primary">
              <%= @available_prompts.first %>
            </div>
          </div>
        </div>
      <% end %>

      <div class="mb-4">
        <label class="form-label fw-medium mb-2">Title:</label>
        <%= f.text_field :title,
            autofocus: true,
            placeholder: "Add a title for your story...",
            class: 'form-control',
            style: "border-radius: 0.5rem;text-indent: 0;",
            autofocus: false
        %>
      </div>

      <div class="mb-4">
        <label class="form-label fw-medium mb-2">Your story:</label>
        <%= f.text_area :content,
            placeholder: "#{@content_value}" || "To be interesting just tell your own story with uncommon honesty...",
            class: 'form-control',
            required: true,
            style: "height: #{local_assigns[:content_height] || '500px'}; border-radius: 0.5rem;text-indent: 0;",
            autofocus: false
        %>
      </div>

      <% if current_user.has_ai_credits? %>
        <div class="mb-4">
          <div class="form-check">
            <%= f.check_box :ai_feedback, class: "form-check-input", id: "ai_feedback_checkbox" %>
            <%= f.label :ai_feedback, "Provide AI-powered feedback to help me improve this story", class: "form-check-label" %>
            <small class="d-block text-muted mt-1">
              You have <%= current_user.ai_feedback_credits %> AI feedback credits remaining
              <span data-bs-toggle="modal" data-bs-target="#aiCreditsModal" style="cursor: pointer;"><i class="fa fa-info-circle fa-xs"></i></span>
            </small>
          </div>
          <div id="ai-feedback-loading-container"></div>
        </div>
      <% end %>

      <div class="d-grid">
        <%= f.submit submit_text, class: "btn btn-primary py-2 fs-5" %>
      </div>
    <% end %>
  </div>
</div>

<div id="feedback-container" data-feedback="<%= @latest_feedback&.content %>" data-timestamp="<%= @latest_feedback&.created_at&.strftime("%b %d, %Y") %>" data-is-new="<%= !!flash[:new_feedback] %>"></div>