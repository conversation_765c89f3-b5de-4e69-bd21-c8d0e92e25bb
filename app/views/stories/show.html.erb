<div class="container mt-5 mb-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow-sm border-0">
        <div class="card-body p-5">
          <article class="blog-post">
            <% if @story.introduction? %>
              <h1 class="fw-bold mb-3">Introduction story</h1>
            <% elsif @story.origin? %>
              <h1 class="fw-bold mb-3">Origin story</h1>
            <% else %>
              <h1 class="fw-bold mb-3"><%= @story.title %></h1>
            <% end %>
            
            <p class="text-muted mb-4">
              <i class="fa-regular fa-calendar me-2"></i><%= @story.created_at.strftime("%B %d, %Y") %>
              <span class="mx-2">•</span>
              <i class="fa-regular fa-user me-2"></i><%= link_to @story.user.name, public_user_stories_path(@story.user), class: "text-decoration-none" %>
            </p>
            
            <hr class="my-4">
            
            <div class="story-content">
              <%= simple_format @story.content, class: 'mb-0 story-text' %>
            </div>
            
            <% if @story.user == current_user %>
              <div class="d-flex justify-content-end mt-4">
                <%= link_to edit_story_path(@story), class: "btn btn-outline-primary" do %>
                  <i class="fa-solid fa-pencil me-2"></i>Edit this story
                <% end %>
              </div>
            <% end %>
          </article>
        </div>
      </div>
      
      <div class="mt-4 text-center">
        <%= link_to public_user_stories_path(@story.user), class: "btn btn-sm btn-outline-secondary" do %>
          <i class="fa-solid fa-arrow-left me-2"></i>Back to <%= @story.user.first_name %>'s stories
        <% end %>
      </div>
    </div>
  </div>
</div>

<style>
  .story-text {
    font-size: 1.1rem;
    line-height: 1.8;
  }
</style>