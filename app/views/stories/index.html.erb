<div class="container">
  <% unless current_user.premium_user? %>
    <%= render "users/upgrade_to_premium" %>
  <% end %>

  <div class="row g-5 py-3">
    <div class="col-lg-4">
      <div class="card storycoach-tools mb-3">
        <div class="card-header">
          <i class="fa fa-pen-fancy me-2"></i>Story Writing Tools
          <span class="badge bg-primary ms-2"><%= pluralize(@stories_drafted, "story") %> written</span>
        </div>
        <ul class="list-group list-group-flush">
          <li class="list-group-item">
            <%= link_to new_story_path, class: "text-decoration-none", data: { turbo: false } do %>
              <div class="d-flex align-items-start">
                <div class="storycoach-tool-icon">
                  <i class="fa fa-feather"></i>
                </div>
                <div>
                  <span class="storycoach-tool-title">Free-Form Story</span>
                  <p class="storycoach-tool-desc">
                    Start with a blank page and write any story you have in mind, with complete creative freedom
                  </p>
                </div>
              </div>
            <% end %>
          </li>
          <% if current_user.premium_user? %>
            <li class="list-group-item">
              <%= link_to new_copy_work_exercise_path, class: "text-decoration-none" do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa fa-copy"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">Copy Work Exercise</span>
                    <p class="storycoach-tool-desc">
                      Warm up your writing muscles by copying the work of great writers to internalize their style
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
            <li class="list-group-item">
              <%= link_to new_introduction_story_path, class: "text-decoration-none" do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa fa-user"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">Introduction Story</span>
                    <p class="storycoach-tool-desc">
                      Create a compelling personal introduction that captures your essence and connects with others
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
            <li class="list-group-item">
              <%= link_to new_origin_story_path, class: "text-decoration-none" do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa fa-rocket"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">Origin Story</span>
                    <p class="storycoach-tool-desc">
                      Craft a powerful narrative about your beginnings, what shaped you, and your journey
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
            <li class="list-group-item">
              <%= link_to new_linked_in_story_path, class: "text-decoration-none", data: { turbo: false } do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa-brands fa-linkedin"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">LinkedIn Post</span>
                    <p class="storycoach-tool-desc">
                      Craft and share a compelling LinkedIn post using storytelling techniques and AI-powered insights
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
          <% else %>
            <li class="list-group-item premium-feature">
              <div class="d-flex align-items-start">
                <div class="storycoach-tool-icon bg-light">
                  <i class="fa fa-star text-warning"></i>
                </div>
                <div>
                  <span class="storycoach-tool-title">Premium Features</span>
                  <p class="storycoach-tool-desc mb-2">
                    Unlock Crash & Burn and Random Word exercises by upgrading to premium.
                  </p>
                  <%= link_to "Upgrade to Premium", new_charge_path, class: "btn btn-sm btn-primary" %>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
        <div class="card-footer">
          <%= link_to user_path(current_user), class: "text-decoration-none d-flex align-items-center" do %>
            <i class="fa fa-home me-2 text-primary"></i>
            <span>Return to Dashboard</span>
          <% end %>
        </div>
      </div>
    </div>
    <% if @stories_drafted.zero? %>
      <div class="col-lg-8">
        <%= render "zero_stories" %>
      </div>
    <% else %>
      <div class="col-lg-8">
        <% @stories.each_with_index do |story, index| %>
          <div class="card mb-3">
            <div class="card-body">
              <div class="row">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="text-muted">
                      <small>
                        <% if story.category.present? %>
                          <%= story.category.humanize %> ·
                        <% elsif story.introduction? %>
                          Introduction story ·
                        <% elsif story.origin? %>
                          Origin story ·
                        <% end %>
                        Last updated <%= story.updated_at.strftime("%B %d, %Y") %>
                      </small>
                    </div>
                    <% if story.published? %>
                      <span class="badge bg-success">Published</span>
                    <% else %>
                      <span class="badge bg-secondary">Draft</span>
                    <% end %>
                  </div>

                  <h5 class="mb-3">
                    <% if story.introduction? %>
                      My Introduction Story
                    <% elsif story.origin? %>
                      My Origin Story
                    <% elsif story.linked_in? %>
                      LinkedIn Post
                    <% else %>
                      <%= story.title.present? ? story.title : "Untitled Story" %>
                    <% end %>
                  </h5>

                  <div class="story-content">
                    <%= truncate(story.content, length: 250) %>
                  </div>

                  <div class="mt-3">
                    <% if story.introduction? %>
                      <%= link_to edit_introduction_story_path(story), class: "btn btn-sm btn-outline-secondary me-2" do %>
                        <i class="fa fa-edit"></i> Edit Story
                      <% end %>
                    <% elsif story.origin? %>
                      <%= link_to edit_origin_story_path(story), class: "btn btn-sm btn-outline-secondary me-2" do %>
                        <i class="fa fa-edit"></i> Edit Story
                      <% end %>
                    <% else %>
                      <%= link_to edit_story_path(story), class: "btn btn-sm btn-outline-secondary me-2" do %>
                        <i class="fa fa-edit"></i> Edit Story
                      <% end %>
                    <% end %>

                    <%= link_to story_path(story), class: "btn btn-sm btn-outline-primary" do %>
                      <i class="fa fa-book-open"></i> View Full Story
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
        <div class="row mb-2">
          <div class="col-lg">
            <div class="d-flex justify-content-end">
              <%== pagy_info(@pagy) if @pagy.pages > 1 %>
            </div>
          </div>
        </div>
        <div class="row mb-5">
          <div class="col-lg">
            <div class="d-flex justify-content-center">
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>