<div class="container mt-5 mb-5">
  <%= render "shared/ai_credits_modal" %>

  <div class="row">
    <div class="col-lg-8 mb-4">
      <h1 class="display-5 fw-bold mb-2">Review and update your story</h1>

      <% if current_user.premium_user? %>
        <div id="publish-panel-container"
             data-story-id="<%= @story.id %>"
             data-published="<%= @story.published %>"
             data-user-id="<%= current_user.id %>">
        </div>
      <% end %>
    </div>
  </div>

  <div class="row flex-lg-row-reverse g-4">
      <% if @latest_feedback.present? %>
        <div class="col-lg-4">
          <!-- React component will render here -->
          <div id="feedback-container"
                data-feedback="<%= @sanitized_feedback_content %>"
                data-timestamp="<%= @latest_feedback.created_at.strftime("%b %d, %Y") %>"
                data-is-new="<%= !!flash[:new_feedback] %>">
          </div>
        </div>
      <% else %>
        <%= render "story_tips_col_4" %>
      <% end %>

    <div class="col-lg-8">
      <%= render "story_form",
          story: @story,
          url: story_path,
          method: :patch,
          submit_text: "Save This Story",
          content_height: "600px",
          show_prompt: false
      %>
    </div>
  </div>
</div>

<!-- React mount points -->
<div id="confetti-container"></div>

<% if flash[:success].present? %>
  <% content_for :body_attributes do %>data-success="true"<% end %>
<% end %>