<%= render "origin_story_example_modal" %>

<div class="container mt-3 mb-3">
  <div class="row d-flex">
    <div class="col-lg-8 mb-3">
      <h1>Update this origin story</h1>
    </div>
    <div class="col-lg-4 mt-3 mb-3">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Need some inspiration?</h5>
          <a href="#" data-bs-toggle="modal" data-bs-target="#originStoryExampleModal">Check out an origin story</a>
        </div>
      </div>
    </div>
  </div>

  <div class="row flex-lg-row-reverse">
    <div class="col-lg-4">
        <%= render "origin_story_tips" %>
      </div>
    </div>

    <div class="col-lg-8">
      <%= form_for @story, url: origin_story_path, method: :patch do |f| %>
        <div class="form-input">
          <%= f.text_area :content,
              placeholder: @origin_story_placeholder,
              class: 'form-control',
              required: true,
              style: "height: 500px;text-indent:0;"
          %>
        </div>

        <%= f.submit "Update this origin story", class: "btn btn-primary" %>
      <% end %>
    </div>
  </div>
</div>