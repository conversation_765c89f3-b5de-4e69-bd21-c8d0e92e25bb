<div class="container" data-controller="story-generation">
  <% unless current_user.premium_user? %>
    <%= render "users/upgrade_to_premium" %>
  <% end %>

  <!-- Mount point for StoryGenerationLoading component -->
  <div id="story-generation-loading-container"></div>

  <div class="row g-5 py-3">
    <div class="col-lg-4">
      <div class="card storycoach-tools mb-3">
        <div class="card-header">
          <i class="fa fa-lightbulb me-2"></i>Tools for Finding Stories
        </div>
        <ul class="list-group list-group-flush">
          <li class="list-group-item">
            <%= link_to new_memory_inspiration_exercise_path, class: "text-decoration-none", data: { turbo: false } do %>
              <div class="d-flex align-items-start">
                <div class="storycoach-tool-icon">
                  <i class="fa fa-quote-right"></i>
                </div>
                <div>
                  <span class="storycoach-tool-title">Use Prompts</span>
                  <p class="storycoach-tool-desc">
                    Answer engaging prompts to help you discover and capture storyworthy memories
                  </p>
                </div>
              </div>
            <% end %>
          </li>
          <li class="list-group-item">
            <%= link_to new_memory_bank_exercise_path, class: "text-decoration-none" do %>
              <div class="d-flex align-items-start">
                <div class="storycoach-tool-icon">
                  <i class="fa fa-bookmark"></i>
                </div>
                <div>
                  <span class="storycoach-tool-title">Save a Memory</span>
                  <p class="storycoach-tool-desc">
                    Capture and store a memorable moment from your life in your memory bank
                  </p>
                </div>
              </div>
            <% end %>
          </li>
          <% if current_user.premium_user? %>
            <li class="list-group-item">
              <%= link_to new_exercise_path, class: "text-decoration-none", data: { turbo: false } do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa fa-fire"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">Crash & Burn Exercise</span>
                    <p class="storycoach-tool-desc">
                      Practice writing without judgment to unlock creativity and new memories
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
            <li class="list-group-item">
              <%= link_to new_random_word_exercise_path, class: "text-decoration-none" do %>
                <div class="d-flex align-items-start">
                  <div class="storycoach-tool-icon">
                    <i class="fa fa-random"></i>
                  </div>
                  <div>
                    <span class="storycoach-tool-title">Random Word Exercise</span>
                    <p class="storycoach-tool-desc">
                      Use random words as creative prompts to spark unexpected memories
                    </p>
                  </div>
                </div>
              <% end %>
            </li>
          <% else %>
            <li class="list-group-item premium-feature">
              <div class="d-flex align-items-start">
                <div class="storycoach-tool-icon bg-light">
                  <i class="fa fa-star text-warning"></i>
                </div>
                <div>
                  <span class="storycoach-tool-title">Premium Features</span>
                  <p class="storycoach-tool-desc mb-2">
                    Unlock Crash & Burn and Random Word exercises by upgrading to premium.
                  </p>
                  <%= link_to "Upgrade to Premium", new_charge_path, class: "btn btn-sm btn-primary" %>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
        <div class="card-footer">
          <%= link_to user_path(current_user), class: "text-decoration-none d-flex align-items-center" do %>
            <i class="fa fa-home me-2 text-primary"></i>
            <span>Return to Dashboard</span>
          <% end %>
        </div>
      </div>
    </div>

    <div class="col-lg-8">

      <% if @user_able_to_recall_memories %>
        <div class="card">
          <div class="card-body">
            <h6 class="card-title text-muted">
              Want to recall a memory? Search your memory bank (using AI)
            </h6>
            <%= form_with url: search_exercises_path, method: :get, local: true, id: "memory-search-form", data: { controller: "search" } do %>
              <div class="input-group">
                <%= text_field_tag :query,
                    params[:query],
                    placeholder: "E.g. When I was exctied about something",
                    class: "form-control",
                    autocomplete: "off",
                    id: "memory-search-input",
                    style: "text-indent: 0;"
                %>
                <div class="input-group-append ms-1 pt-1">
                  <%= submit_tag "Look in my Memory Bank", class: "btn btn-primary" %>
                </div>
              </div>
            <% end %>

            <div id="search-results-container" class="mt-3">
              <% if flash[:notice] && params[:query].present? %>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                  <%= flash[:notice] %>
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <% flash.delete(:notice) %>
              <% end %>

              <% if @search_result && @search_result.any? %>
                <h6 class="text-muted">Semantic search results for "<%= params[:query] %>":</h6>

                <% @search_result.each_with_index do |result, index| %>
                  <div class="search-result-card p-3 border rounded mt-2 <%= index == 0 ? 'border-primary' : '' %>">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="text-muted mb-1">
                        <small>
                          <%= result["category"].to_s.humanize %> on <%= result["date"] %>
                          <% if result["match_score"] %>
                            <span class="badge bg-info ms-2"><%= result["match_score"] %></span>
                          <% end %>
                        </small>
                      </div>
                      <% if index == 0 %>
                        <span class="badge bg-primary">Best match</span>
                      <% end %>
                    </div>
                    <div class="memory-content">
                      <%= simple_format result["memory"] %>
                    </div>
                    <% if current_user.premium_user? %>
                      <%= button_to stories_path(memory: result["memory"], draft_story: "true"), method: :post, class: "btn btn-sm btn-outline-primary mt-2 story-generation-btn" do %>
                        <i class="fa fa-lightbulb"></i> Craft a story from this memory
                      <% end %>
                    <% else %>
                      <%= link_to new_story_path(content: result["memory"]), class: "btn btn-sm btn-outline-primary mt-2" do %>
                        <i class="fa fa-lightbulb"></i> Craft a story from this memory
                      <% end %>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>

        <hr class="soften">
      <% end %>

      <% if @exercises.empty? %>
        <%= render "zero_notes" %>
      <% else %>
        <h5>Your Memory Bank</h5>
      <% end %>

      <% @exercises.each_with_index do |note, index| %>
        <div class="card mb-3">
          <div class="card-body">
            <div class="row">
              <div class="<%= note.photo.attached? ? 'col-md-9' : 'col-12' %>">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <div class="text-muted">
                    <small>
                      <%= note.category.humanize %> on <%= note.note_date.strftime("%B %d, %Y") %>
                    </small>
                  </div>
                </div>

                <div class="memory-content">
                  <%= simple_format note.note %>
                </div>

                <div class="mt-3">
                  <div class="d-flex justify-content-start">
                    <%= link_to(edit_exercise_path(note), class: "btn btn-sm btn-outline-secondary me-2") do %>
                      <i class="fa fa-edit"></i> Edit/Update
                    <% end %>
                    <% if current_user.premium_user? %>
                      <%= button_to stories_path(memory: note.note, draft_story: "true"), method: :post, class: "btn btn-sm btn-outline-primary story-generation-btn" do %>
                        <i class="fa fa-lightbulb"></i> Craft a story from this memory
                      <% end %>
                    <% else %>
                      <%= link_to new_story_path(content: note.note), class: "btn btn-sm btn-outline-primary" do %>
                        <i class="fa fa-lightbulb"></i> Craft a story from this memory
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>

              <% if note.photo.attached? %>
                <div class="col-md-3 mt-3 mt-md-0">
                  <%= image_tag note.photo.url, class: "img-fluid rounded", alt: note.photo.name, loading:"lazy" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <div class="row mb-2">
        <div class="col-md">
          <div class="d-flex justify-content-end">
            <%== pagy_info(@pagy) if @pagy.pages > 1 %>
          </div>
        </div>
      </div>
      <div class="row mb-5">
        <div class="col-md">
          <div class="d-flex justify-content-center">
            <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
          </div>
        </div>
      </div>
    </div>
  </div>


</div>