<div class="container mt-5 mb-5">
  <div class="row d-flex">
    <div class="col-md-8">
      <h3>Update memory for <%= @exercise.note_date.strftime("%b %d") %></h3>
    </div>
  </div>

  <div class="row flex-lg-row-reverse g-5 py-5">
    <div class="col-md-4">
      <div class="card px-3 py-3">
        <p>
          Tips for Homework for Life exercise:
        </p>
        <div class="list-group">
          <div class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
            <i class="fa-solid fa-lightbulb" aria-hidden="true" class="rounded-circle flex-shrink-0"></i>
            <div class="d-flex gap-2 w-100 justify-content-between">
              <div>
                <h6 class="mb-0">Storyworth moment</h6>
                <p class="mb-0 opacity-75">Think of a moment from the day that could be a good story to share.</p>
              </div>
            </div>
          </div>
          <div class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
            <i class="fa-solid fa-gift" class="rounded-circle flex-shrink-0"></i>
            <div class="d-flex gap-2 w-100 justify-content-between">
              <div>
                <h6 class="mb-0">Keep it short</h6>
                <p class="mb-0 opacity-75">Keep it under a sentence or two. It doesn't even need to be carefully written with precise details.</p>
              </div>
            </div>
          </div>
          <div class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
            <i class="fa-solid fa-star" class="rounded-circle flex-shrink-0"></i>
            <div class="d-flex gap-2 w-100 justify-content-between">
              <div>
                <h6 class="mb-0">Do it everyday</h6>
                <p class="mb-0 opacity-75">Reply to the daily email or do it here.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <%= form_for @exercise, url: exercise_path(@exercise), html: { multipart: true }, method: :patch do |f| %>
        <div class="input-group">
          <span class="input-group-text">Memory Date:</span>
          <%= f.select :month,
            (1..12).to_a,
            { selected: @exercise.note_date.month, required: true},
            { class: "form-select" }
          %>
          <%= f.select :day,
            (1..31).to_a,
            { selected: @exercise.note_date.day, required: true },
            { class: "form-select" }
          %>
          <%= f.select :year,
            ((Time.now.year - 20)..Time.now.year).to_a,
            { selected: @exercise.note_date.year, required: true},
            { class: "form-select" }
          %>
        </div>

        <div class="form-input">
          <%= f.text_area :note,
              autofocus: true,
              placeholder: "Start writing whatever words come to your mind...",
              class: 'form-control',
              required: true,
              style: "height: 500px;text-indent:0;"
          %>
        </div>

        <div class="mb-3">
          <label for="formFile" class="form-label">Attach a photo to this memory:</label>
          <%= f.file_field :photo,
            class: "form-control",
            required: false
          %>
        </div>

        <%= f.submit "Save your memory", class: "btn btn-primary" %>
      <% end %>
    </div>

  </div>
</div>