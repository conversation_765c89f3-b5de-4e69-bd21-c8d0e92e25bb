<% unless @random_user_memory.nil? %>
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="text-muted m-0">Memory Flashback</h6>
      <span class="badge bg-secondary"><%= @random_user_memory.note_date.strftime("%B %d, %Y") %></span>
    </div>
    <div class="card-body">
      <p class="card-text"><%= simple_format @random_user_memory.note %></p>
      <div class="d-flex justify-content-end">

        <% if current_user.premium_user? %>
          <%= button_to stories_path(memory: @random_user_memory.note, draft_story: "true"), method: :post, class: "btn btn-sm btn-outline-primary story-generation-btn me-2" do %>
            <i class="fa fa-lightbulb me-1"></i>Craft a story
          <% end %>
        <% else %>
          <%= link_to new_story_path(content: @random_user_memory.note), class: "btn btn-sm btn-outline-primary me-2" do %>
            <i class="fa fa-lightbulb me-1"></i>Craft a story
          <% end %>
        <% end %>
        <%= link_to exercises_path, class: "btn btn-sm btn-outline-secondary" do %>
          <i class="fa fa-book me-1"></i>View all memories
        <% end %>
      </div>
    </div>
  </div>
<% end %>

