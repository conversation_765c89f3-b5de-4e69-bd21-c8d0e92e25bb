<div class="container" data-controller="story-generation">
  <%= render "shared/ai_credits_modal" %>
  <%= render "shared/level_and_points_modal" %>
  <%= render "shared/memory_saved_celebration" %>

  <!-- Mount point for StoryGenerationLoading component -->
  <div id="story-generation-loading-container"></div>

  <div class="row">
    <div class="col-lg-4">
      <div class="card user-profile-card mb-3">
        <!-- Profile Header with Avatar -->
        <div class="user-profile-header">
          <div class="avatar-container">
            <%= image_tag avatar_path(current_user), class: "avatar-image" %>
            <% if current_user.premium_user? %>
              <div class="premium-badge">
                <i class="fa fa-star"></i>
              </div>
            <% end %>
          </div>
        </div>

        <!-- User Stats -->
        <div class="user-stats">
          <div class="stat-item">
            <div class="stat-value"><%= current_user.level %></div>
            <div class="stat-label">Level</div>
          </div>

          <div class="stat-item">
            <div class="stat-value"><%= @ai_feedback_credits %></div>
            <div class="stat-label">
              <span data-bs-toggle="modal" data-bs-target="#aiCreditsModal" style="cursor: pointer;">
                AI Credits <i class="fa fa-info-circle fa-xs"></i>
              </span>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-value"><%= @points_to_level_up %></div>
            <div class="stat-label">
              <span data-bs-toggle="modal" data-bs-target="#pointsAndLevelsModal" style="cursor: pointer;">
                To Level Up <i class="fa fa-info-circle fa-xs"></i>
              </span>
            </div>
          </div>
        </div>

        <!-- Profile Info -->
        <div class="profile-info">
          <h2 class="profile-name"><%= current_user.name %></h2>

          <div class="profile-detail">
            <i class="fa fa-calendar"></i>
            <span>Storyteller since <%= @user.created_at.strftime("%B %d, %Y") %></span>
          </div>

          <div class="profile-detail">
            <i class="fa fa-bell"></i>
            <span>Practice reminder: <%= @practice_frequency.humanize %></span>
          </div>

          <% if current_user.premium_user? %>
            <div class="profile-detail">
              <i class="fa fa-link"></i>
              <span><%= link_to "Published Stories Collection", public_user_stories_path(current_user), target: "_blank" %></span>
            </div>
          <% end %>

          <% unless current_user.premium_user? %>
            <div class="mt-3">
              <%= link_to new_charge_path, class: "btn btn-warning btn-sm w-100" do %>
                <i class="fa fa-star me-1"></i> Upgrade to Premium Access
              <% end %>
            </div>
          <% end %>
        </div>

        <!-- Profile Footer -->
        <div class="profile-footer">
          <%= link_to edit_user_registration_path, class: "profile-settings-btn text-decoration-none" do %>
            <i class="fa fa-cog"></i> Manage Account Settings
          <% end %>
        </div>
      </div>

      <div class="row">
        <div class="col mt-3 mb-3">
          <div class="card storycoach-tools">
            <div class="card-header">
              <i class="fa fa-toolbox me-2"></i>StoryCoach Tools
            </div>
            <ul class="list-group list-group-flush">
              <li class="list-group-item">
                <%= link_to exercises_path, class: "text-decoration-none" do %>
                  <div class="d-flex align-items-start">
                    <div class="storycoach-tool-icon">
                      <i class="fa fa-search"></i>
                    </div>
                    <div>
                      <span class="storycoach-tool-title">Find stories</span>
                      <p class="storycoach-tool-desc">
                        Level up your story-telling skills using bite-sized exercises. Capture memories or practice
                        writing and resurrect forgotten moments.
                      </p>
                    </div>
                  </div>
                <% end %>
              </li>
              <li class="list-group-item">
                <%= link_to stories_path, class: "text-decoration-none" do %>
                  <div class="d-flex align-items-start">
                    <div class="storycoach-tool-icon">
                      <i class="fa fa-pen-fancy"></i>
                    </div>
                    <div>
                      <span class="storycoach-tool-title">Craft stories</span>
                      <p class="storycoach-tool-desc">
                        Start writing your stories and create drafts. If you get stuck, use the power of AI
                        feedback to help you get inspired and improve.
                      </p>
                    </div>
                  </div>
                <% end %>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-8">
      <div class="card shadow-sm border-0">
        <div class="card-body p-4">
          <h5 class="card-title mb-3">
            <i class="fa fa-bookmark me-2 text-primary"></i>What moment would you like to remember?
          </h5>
          <div class="card-text">
          <%= form_for @exercise, url: memory_bank_exercises_path, html: { multipart: true, class: "memory-capture-form" }, method: :post do |f| %>
            <div class="mb-3">
              <label class="form-label fw-medium">When did this happen?</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fa fa-calendar-alt"></i></span>
                <%= f.select :month,
                  Date::MONTHNAMES.compact.each_with_index.map { |name, i| [name, i + 1] },
                  { selected: Time.zone.now.month, required: true},
                  { class: "form-select" }
                %>
                <%= f.select :day,
                  (1..31).to_a,
                  { selected: Time.zone.now.day, required: true },
                  { class: "form-select" }
                %>
                <%= f.select :year,
                  ((Time.now.year - 20)..Time.now.year).to_a.reverse,
                  { selected: Time.zone.now.year, required: true},
                  { class: "form-select" }
                %>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label fw-medium">Your memory</label>
              <%= f.text_area :note,
                  autofocus: true,
                  placeholder: "Describe a memorable moment you'd like to preserve...",
                  class: 'form-control',
                  required: true,
                  style: "height: 120px;border-radius:0.5rem;text-indent:0;"
              %>
            </div>

            <%= f.hidden_field :source, value: 'users_show' %>

            <div class="d-grid">
              <%= f.submit "Save your memory", class: "btn btn-primary py-2" %>
            </div>
          <% end %>
          </div>
        </div>
      </div>

      <hr class="soften">

      <% if @latest_memory.present? %>
        <div class="<%= flash[:memory_saved] ? 'memory-highlight highlight-entry-animation' : '' %>">
          <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="text-muted m-0">Your Latest Memory</h6>
              <span class="badge bg-secondary"><%= @latest_memory.note_date.strftime("%B %d, %Y") %></span>
            </div>
            <div class="card-body">
              <p class="card-text"><%= simple_format @latest_memory.note %></p>
              <div class="d-flex justify-content-end">
                <% if current_user.premium_user? %>
                  <%= button_to stories_path(memory: @latest_memory.note, draft_story: "true"), method: :post, class: "btn btn-sm btn-outline-primary story-generation-btn" do %>
                    <i class="fa fa-lightbulb me-1"></i>Craft a story from this memory
                  <% end %>
                <% else %>
                  <%= link_to new_story_path(content: @latest_memory.note), class: "btn btn-sm btn-outline-primary" do %>
                    <i class="fa fa-lightbulb me-1"></i>Craft a story from this memory
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <h6 class="text-muted text-uppercase ms-2">Your Story Feed</h6>

      <%= render "inspiration_for_the_day" %>

      <%= render "random_user_memory" %>

      <div class="row">
        <div class="col">
          <% @public_stories.each_with_index do |story, index| %>
            <div class="card mb-4">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="text-muted m-0">Community Story</h6>
                <span class="badge bg-<%= ['primary', 'secondary', 'success', 'info', 'warning'][index % 5] %>">
                  <%= story.category&.humanize || "Story" %>
                </span>
              </div>
              <div class="card-body">
                <h5 class="mb-3"><%= story.title %></h5>
                <p class="card-text"><%= truncate(story.content, length: 256) %></p>
                <div class="d-flex justify-content-end">
                  <%= link_to story_path(story), class: "btn btn-sm btn-outline-primary", target: "_blank" do %>
                    <i class="fa fa-book-open me-1"></i>Read full story
                  <% end %>
                </div>
              </div>
              <div class="card-footer d-flex justify-content-between align-items-center">
                <small class="text-muted">Published by <%= story.user.first_name %></small>
                <small class="text-muted"><%= time_ago_in_words(story.created_at) %> ago</small>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
