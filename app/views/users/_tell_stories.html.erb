  <% if current_user.premium_user? %>

    <div class="row g-3 py-1">
      <div class="col-md-12">
        <div class="card mb-2 bg-white">
          <h5 class="card-header">Tell stories</h5>
          <div class="card-body">
            <p class="card-text">
              Publish your stories and share them with the world.
            </p>
            <p>
              Your shared stories are available <%= link_to "here", public_user_stories_path(current_user), target: "_blank" %>:
            <% @stories.each_with_index do |story, index| %>
              <div class="card">
                <div class="card-header">
                  <% if story.introduction? %>
                    Introduction story
                  <% elsif story.origin? %>
                    Origin story
                  <% else %>
                    <%= story.title %>
                  <% end %>
                </div>
                <div class="card-body">
                  <p class="card-text">
                    <%= truncate(story.content, length: 256) %>
                    <%= link_to "(continued)", story_path(story) %>
                  </p>
                  <% if story.published %>
                    <%= link_to "Unpublish", unpublish_story_path(story), class: "card-link", method: :patch %>
                  <% else %>
                    <%= link_to "Publish", publish_story_path(story), class: "card-link", method: :patch %>
                  <% end %>
                  <% if story.introduction? %>
                    <%= link_to "Edit", edit_introduction_story_path(story), class: "card-link" %>
                  <% elsif story.origin? %>
                    <%= link_to "Edit", edit_origin_story_path(story), class: "card-link" %>
                  <% else %>
                    <%= link_to "Edit", edit_story_path(story) %>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

  <% end %>