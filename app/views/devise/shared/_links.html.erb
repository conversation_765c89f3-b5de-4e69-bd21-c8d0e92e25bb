<%- if controller_name != 'sessions' %>
  <div class="mb-2">
    Already have an account? <%= link_to "Login", new_session_path(resource_name), class: "text-primary" %>
  </div>
<% end %>

<%- if devise_mapping.registerable? && controller_name != 'registrations' %>
  <div class="mb-2">
    Don't have an account? <%= link_to "Sign up", new_registration_path(resource_name), class: "text-primary" %>
  </div>
<% end %>

<%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
  <div class="mb-2">
    <%= link_to "Forgot your password?", new_password_path(resource_name), class: "text-primary" %>
  </div>
<% end %>

<%- if devise_mapping.confirmable? && controller_name != 'confirmations' %>
  <div class="mb-2">
    <%= link_to "Didn't receive confirmation instructions?", new_confirmation_path(resource_name), class: "text-primary" %>
  </div>
<% end %>

<%- if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
  <div class="mb-2">
    <%= link_to "Didn't receive unlock instructions?", new_unlock_path(resource_name), class: "text-primary" %>
  </div>
<% end %>