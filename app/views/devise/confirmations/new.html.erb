<% title "Resend confirmation instructions" %>
<div class="container mt-5 mb-5">
  <div class="row d-flex align-items-center justify-content-center">
    <div class="col-lg-6">
      <div class="card px-5 py-5">
        <h5 class="mt-3">Resend confirmation instructions</h5>

        <%= form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post }) do |f| %>
          <%= render "devise/shared/error_messages", resource: resource %>

          <div class="form-input">
            <i class="fa fa-envelope"></i>
            <%= f.email_field :email, autofocus: true, class: "form-control", value: (resource.pending_reconfirmation? ? resource.unconfirmed_email : resource.email) %>
          </div>

          <%= f.submit "Resend confirmation instructions", class: "btn btn-primary signup" %>
        <% end %>

        <div class="text-center">
          <%= render "devise/shared/links" %>
        </div>
      </div>
    </div>
  </div>
</div>