<div class="row">
  <div class="col-lg-4 offset-lg-4">
    <h2 class="text-center">Change your password</h2>

    <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      <%= f.hidden_field :reset_password_token %>

      <div class="mb-3">
        <%= f.password_field :password, autofocus: true, autocomplete: "off", class: 'form-control', placeholder: "Password" %>
        <% if @minimum_password_length %>
          <p class="text-muted"><small><%= @minimum_password_length %> characters minimum</small></p>
        <% end %>
      </div>

      <div class="mb-3">
        <%= f.password_field :password_confirmation, autocomplete: "off", class: 'form-control', placeholder: "Confirm Password" %>
      </div>

      <div class="mb-3 d-grid">
        <%= f.submit "Change my password", class: 'btn btn-primary btn-lg' %>
      </div>
    <% end %>

    <div class="text-center">
      <%= render "devise/shared/links" %>
    </div>
  </div>
</div>
