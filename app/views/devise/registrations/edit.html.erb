<div class="container">
<div class="row">
  <div class="col-lg-6 offset-lg-3">
    <h3 >Manage your account</h3>

    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>

      <div class="form-floating mb-3">
        <%= f.text_field :name,
          id: "nameInput",
          autofocus: false,
          class: "form-control",
          placeholder: "Full name",
          style: "text-indent: 0;"
        %>
        <label for="nameInput">Fullname</label>
      </div>

      <div class="form-floating mb-3">
        <%= f.email_field :email,
          id: "emailInput",
          class: "form-control",
          placeholder: "Email Address" ,
          style: "text-indent: 0;"
        %>
        <label for="emailInput">Email Address</label>
      </div>

      <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
        <div class="alert alert-warning">Currently waiting confirmation for: <%= resource.unconfirmed_email %></div>
      <% end %>

      <div class="form-floating mb-3">
        <%= f.password_field :password,
          id: "passwordInput",
          autocomplete: "off",
          class: "form-control",
          placeholder: "Password",
          style: "text-indent: 0;"
        %>
        <label for="passwordInput">Change Password</label>
        <p class="form-text text-muted"><small>Leave password blank if you don't want to change it</small></p>
      </div>

      <div class="form-floating mb-3">
        <%= f.password_field :password_confirmation,
          id: "confirmPasswordInput",
          autocomplete: "off",
          class: "form-control",
          placeholder: "Confirm Password",
          style: "text-indent: 0;"
        %>
        <label for="confirmPasswordInput">Confirm Password</label>
      </div>

      <div class="form-floating mb-3">
        <%= f.password_field :current_password,
          id: "currentPasswordInput",
          autocomplete: "off",
          class: "form-control",
          placeholder: "Current Password",
          style: "text-indent: 0;"
        %>
        <label for="currentPasswordInput">Current Password</label>
        <p class="form-text text-muted"><small>We need your current password to confirm your changes</small></p>
      </div>

      <div class="mb-3 d-grid">
        <%= f.submit "Save Changes", class: 'btn btn-primary' %>
      </div>
    <% end %>

    <hr>

    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Your preferences</h5>
        <p class="card-text">

          <%= form_for current_user, url: user_path(current_user), html: { multipart: true }, method: :patch do |f| %>
            <div class="mb-3">
              <label for="practiceFrequency" class="form-label">⏰ How frequently do you want to do practice exercises?</label>
              <%= f.select :practice_frequency,
                            User.practice_frequencies.keys.map { |k| [k.humanize, k] },
                            { selected: current_user.practice_frequency},
                            { class: "form-select" }
              %>
            </div>

            <div class="mb-3">
              <label for="practiceTimeOfDay" class="form-label">🕒 At what time of the day would you like StoryCoach to send you a practice reminder?</label>
              <%= f.select :practice_time_of_day,
                            User.practice_time_of_days.keys.map { |k| [k.humanize, k] },
                            { selected: current_user.practice_time_of_day},
                            { class: "form-select" }
              %>
            </div>

            <div class="mb-3">
              <label for="goal" class="form-label">🎯 What's your goal for storytelling?</label>
              <div class="form-input">
                <%= f.text_area :goal,
                  autofocus: false,
                  placeholder: "To tell better stories to my family and friends",
                  class: "form-control",
                  required: false,
                  style: "height: 100px;text-indent:0;"
                %>
              </div>
            </div>
            <div class="mb-3 d-grid">
              <%= f.submit "Save your preferences", class: "btn btn-primary" %>
            </div>
          <% end %>
        </p>
      </div>
    </div>

    <hr>

    <p class="text-center"><%= link_to "Deactivate my account", registration_path(resource_name), data: { confirm: "Are you sure? You cannot undo this." }, method: :delete %></p>
  </div>
</div>
</div>