<% title "Sign Up" %>
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="row">
        <div class="col-lg-5 mb-4 mb-lg-0">
          <div class="card border-0 shadow-sm h-100 user-profile-card">
            <div class="card-body p-4">
              <div class="text-center mb-4">
                <div class="storycoach-tool-icon mb-3">
                  <i class="fa fa-pen-fancy fa-3x text-primary"></i>
                </div>
                <h2 class="h4">Start Your Storytelling Journey</h2>
                <p class="text-muted">Create your account and unlock your potential today</p>
              </div>

              <div class="d-none d-lg-block">
                <div class="card bg-light mb-3">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-start">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-search text-success"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Find Stories</h6>
                        <p class="small mb-0 mt-1">Discover meaningful moments in your life</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card bg-light mb-3">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-start">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-pen-nib text-info"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Craft Stories</h6>
                        <p class="small mb-0 mt-1">Transform memories into captivating narratives</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card bg-light">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-start">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-share-alt text-warning"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Share Stories</h6>
                        <p class="small mb-0 mt-1">Connect with others through powerful storytelling</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-7">
          <div class="card shadow-sm">
            <div class="card-header bg-primary text-white py-3">
              <h4 class="m-0"><i class="fa fa-user-plus me-2"></i>Create Your Account</h4>
            </div>
            <div class="card-body p-4">
              <%= form_for(resource, as: resource_name, url: registration_path(resource_name), data: { turbo: "false" }) do |f| %>
                <%= render "devise/shared/error_messages", resource: resource %>

                <div class="form-input">
                  <label class="form-label">Your Name</label>
                  <div class="input-group">
                    <div class="d-flex w-100">
                      <%= f.text_field :first_name, autofocus: true, class: "form-control rounded-0 rounded-top", placeholder: "First name", required: true, autocomplete: "off" %>
                      <%= f.text_field :last_name, autofocus: false, class: "form-control rounded-0 rounded-bottom border-top-0", placeholder: "Last name", required: false, autocomplete: "off" %>
                    </div>
                  </div>
                </div>

                <div class="form-input mt-3">
                  <label class="form-label">Email Address</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light">
                      <i class="fa fa-envelope text-primary"></i>
                    </span>
                    <%= f.email_field :email, autofocus: false, class: "form-control", placeholder: "Your email address", required: true, autocomplete: "off" %>
                  </div>
                </div>

                <div class="form-input mt-3">
                  <label class="form-label">Password</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light">
                      <i class="fa fa-lock text-primary"></i>
                    </span>
                    <%= f.password_field :password, placeholder: "Create a password", class: "form-control", required: true, autocomplete: "off" %>
                  </div>
                  <small class="text-muted">Must be at least 6 characters</small>
                </div>

                <div class="mt-3">
                  <%= recaptcha_tags %>
                </div>

                <div class="d-grid mt-4">
                  <%= f.submit "Sign up for #{Rails.configuration.application_name}", class: "btn btn-primary signup" %>
                </div>
              <% end %>

              <div class="text-center my-4">
                <div class="divider">
                  <span class="divider-text">Or sign up with</span>
                </div>
              </div>

              <div class="text-center">
                <%- if devise_mapping.omniauthable? %>
                  <%- resource_class.omniauth_providers.each do |provider| %>
                    <%= button_to omniauth_authorize_path(resource_name, provider), method: :post, data: {turbo: "false"}, class: "btn btn-outline-secondary btn-social-icon" do %>
                      <div class="d-flex align-items-center justify-content-center">
                        <%= image_tag "landing_page/Google-G-logo.png", class: "google-icon me-2", width: 18, height: 18 %>
                        <span>Sign up with Google</span>
                      </div>
                    <% end %>
                  <% end %>
                <% end %>
              </div>

              <div class="mt-4 text-center">
                <%= render "devise/shared/links" %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .divider {
    display: flex;
    align-items: center;
    margin: 1rem 0;
  }

  .divider-text {
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
  }

  .divider::before, .divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #dee2e6;
  }

  .btn-social-icon {
    width: 100%;
    padding: 0.5rem 1rem;
  }

  .form-input .input-group-text {
    width: 45px;
    justify-content: center;
  }

  .form-input input.form-control {
    height: 45px;
    text-indent: 5px;
  }
</style>