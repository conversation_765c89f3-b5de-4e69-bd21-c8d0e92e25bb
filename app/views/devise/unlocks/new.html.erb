<div class="row">
  <div class="col-lg-4 offset-lg-4">
    <h1 class="text-center">Resend unlock instructions</h1>

    <%= form_for(resource, as: resource_name, url: unlock_path(resource_name), html: { method: :post }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>

      <div class="mb-3">
        <%= f.label :email, class: "form-label" %>
        <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "form-control" %>
      </div>

      <div class="mb-3">
        <%= f.submit "Resend unlock instructions", class: "btn btn-lg btn-primary" %>
      </div>
    <% end %>

    <%= render "devise/shared/links" %>
  </div>
</div>
