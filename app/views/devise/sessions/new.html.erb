<% title "Login" %>
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="row">
        <div class="col-lg-5 mb-4 mb-lg-0">
          <div class="card border-0 shadow-sm h-100 user-profile-card">
            <div class="card-body p-4">
              <div class="text-center mb-4">
                <div class="storycoach-tool-icon mb-3">
                  <i class="fa fa-book-open fa-3x text-primary"></i>
                </div>
                <h2 class="h4">Welcome back to StoryCoach</h2>
                <p class="text-muted">Continue your storytelling journey</p>
              </div>
              
              <div class="d-none d-lg-block">
                <div class="card bg-light mb-4">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-pen text-primary"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Craft Your Stories</h6>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card bg-light mb-4">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-robot text-primary"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Get AI Feedback</h6>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="card bg-light">
                  <div class="card-body py-3">
                    <div class="d-flex align-items-center">
                      <div class="storycoach-tool-icon me-3">
                        <i class="fa fa-users text-primary"></i>
                      </div>
                      <div>
                        <h6 class="m-0">Share With Community</h6>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-lg-7">
          <div class="card shadow-sm">
            <div class="card-header bg-primary text-white py-3">
              <h4 class="m-0"><i class="fa fa-sign-in-alt me-2"></i>Login to Your Account</h4>
            </div>
            <div class="card-body p-4">
              <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
                <%= render "devise/shared/error_messages", resource: resource %>
                
                <div class="form-input">
                  <label class="form-label">Email Address</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light">
                      <i class="fa fa-envelope text-primary"></i>
                    </span>
                    <%= f.email_field :email, autofocus: true, placeholder: 'Enter your email', class: 'form-control', required: true %>
                  </div>
                </div>

                <div class="form-input mt-3">
                  <label class="form-label">Password</label>
                  <div class="input-group">
                    <span class="input-group-text bg-light">
                      <i class="fa fa-lock text-primary"></i>
                    </span>
                    <%= f.password_field :password, autocomplete: "off", placeholder: 'Enter your password', class: 'form-control', required: true %>
                  </div>
                </div>

                <% if devise_mapping.rememberable? -%>
                  <div class="form-check mt-3">
                    <label class="form-check-label">
                      <%= f.check_box :remember_me, class: "form-check-input" %>
                      Remember me
                    </label>
                  </div>
                <% end -%>

                <div class="d-grid mt-4">
                  <%= f.submit "Login to #{Rails.configuration.application_name}", class: "btn btn-primary signup" %>
                </div>
              <% end %>
              
              <div class="text-center my-4">
                <div class="divider">
                  <span class="divider-text">Or continue with</span>
                </div>
              </div>

              <div class="text-center">
                <%- if devise_mapping.omniauthable? %>
                  <%- resource_class.omniauth_providers.each do |provider| %>
                    <%= button_to omniauth_authorize_path(resource_name, provider), method: :post, data: {turbo: "false"}, class: "btn btn-outline-secondary btn-social-icon" do %>
                      <div class="d-flex align-items-center justify-content-center">
                        <%= image_tag "landing_page/Google-G-logo.png", class: "google-icon me-2", width: 18, height: 18 %>
                        <span>Sign in with Google</span>
                      </div>
                    <% end %>
                  <% end %>
                <% end %>
              </div>
              
              <div class="mt-4 text-center">
                <%= render "devise/shared/links" %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .divider {
    display: flex;
    align-items: center;
    margin: 1rem 0;
  }
  
  .divider-text {
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  .divider::before, .divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #dee2e6;
  }
  
  .btn-social-icon {
    width: 100%;
    padding: 0.5rem 1rem;
  }
  
  .form-input .input-group-text {
    width: 45px;
    justify-content: center;
  }
  
  .form-input input.form-control {
    height: 45px;
    text-indent: 5px;
  }
</style>