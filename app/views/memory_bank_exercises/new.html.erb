<div class="container mt-5 mb-5">
  <div class="row">
    <div class="col-md-8 mb-4">
      <h1 class="display-5 fw-bold mb-2">Save a Storyworthy Memory</h1>
      <p class="lead text-secondary">
        Make a deposit in your memory bank. Capture a moment that you'd like to remember and write about someday.
      </p>
    </div>
  </div>

  <div class="row flex-lg-row-reverse g-4">
    <div class="col-md-4">
      <div class="card shadow-sm border-0 mb-4">
        <div class="card-header bg-light py-3">
          <h5 class="mb-0">
            <i class="fa-solid fa-lightbulb text-warning me-2"></i>Memory Capture Tips
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-primary bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-star text-primary"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">Storyworthy Moment</h6>
                <p class="text-secondary mb-0 small">Think of a moment from the day that could be a good story to share.</p>
              </div>
            </div>

            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-success bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-gift text-success"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">Keep It Short</h6>
                <p class="text-secondary mb-0 small">A sentence or two is enough. You don't need precise details at this stage.</p>
              </div>
            </div>

            <div class="list-group-item border-0 d-flex align-items-start p-3">
              <div class="me-3">
                <div class="bg-warning bg-opacity-10 rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fa-solid fa-lightbulb text-warning"></i>
                </div>
              </div>
              <div>
                <h6 class="fw-bold mb-1">Can't Think of a Moment?</h6>
                <p class="text-secondary mb-0 small"><%= @daily_prompt.question %></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <div class="card shadow-sm border-0">
        <div class="card-body p-4">
          <%= form_for @exercise, url: memory_bank_exercises_path, html: { multipart: true, class: "memory-capture-form" }, method: :post do |f| %>
            <div class="mb-4">
              <label class="form-label fw-medium mb-2">When did this happen?</label>
              <div class="input-group">
                <span class="input-group-text bg-light border">
                  <i class="fa-solid fa-calendar-alt text-primary"></i>
                </span>
                <%= f.select :month,
                  Date::MONTHNAMES.compact.each_with_index.map { |name, i| [name, i + 1] },
                  { selected: Time.zone.now.month, required: true},
                  { class: "form-select" }
                %>
                <%= f.select :day,
                  (1..31).to_a,
                  { selected: Time.zone.now.day, required: true },
                  { class: "form-select" }
                %>
                <%= f.select :year,
                  ((Time.now.year - 20)..Time.now.year).to_a.reverse,
                  { selected: Time.zone.now.year, required: true},
                  { class: "form-select" }
                %>
              </div>
            </div>

            <div class="mb-4">
              <label class="form-label fw-medium mb-2">Describe your memory:</label>
              <%= f.text_area :note,
                  autofocus: true,
                  placeholder: "Describe a memorable moment you'd like to preserve...",
                  class: "form-control",
                  required: true,
                  style: "height: 240px; border-radius: 0.5rem;"
              %>
            </div>

            <div class="mb-4">
              <label class="form-label fw-medium mb-2">
                <i class="fa-solid fa-image text-primary me-1"></i>
                Attach a photo (optional):
              </label>
              <%= f.file_field :photo,
                class: "form-control",
                required: false
              %>
            </div>

            <div class="d-grid">
              <%= f.submit "Save Your Memory", class: "btn btn-primary py-2 fs-5" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>