class User < ApplicationRecord
  extend FriendlyId
  friendly_id :slug_candidates, use: :slugged

  def slug_candidates
    [
      :base_slug,
      -> { "#{base_slug}-#{SecureRandom.random_number(100)}" } # Generates a simple number (0-99) for uniqueness
    ]
  end

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable and :omniauthable
  devise :database_authenticatable, :registerable, :recoverable, :rememberable, :validatable, :trackable, :omniauthable, omniauth_providers: [:google_oauth2]

  include UserLevelsConcern

  DEFAULT_AI_CREDITS_ALLOCATION = 0

  enum practice_frequency: { daily: 0, weekly: 1, never: 2 }
  enum practice_time_of_day: { morning: 0, afternoon: 1, evening: 2, random: 3 }

  has_one_attached :avatar
  has_person_name

  has_many :services, dependent: :destroy
  has_many :exercises, dependent: :destroy
  has_many :badges, dependent: :destroy
  has_many :stories, dependent: :destroy

  after_create :allocate_ai_feedback_credits_points_and_level

  after_create_commit :send_welcome_email,
                      :allocate_first_badge,
                      :send_admin_notification

  before_create :check_duplicate_name_and_email unless Rails.env.test?
  before_create :generate_api_key

  validates :practice_time_of_day, presence: true
  validates :practice_frequency, presence: true
  validates :name, presence: true
  before_validation :set_default_name, if: -> { name.blank? }

  def premium_user?
    stripe_token.present? || admin? || premium_subscriber
  end

  def has_ai_credits?
    ai_feedback_credits.positive?
  end

  private

  def set_default_name
    self.name = "Default Name"
  end

  def generate_api_key
    self.api_key = SecureRandom.hex(20) unless api_key.present?
  end

  def base_slug
    name
  end

  def send_welcome_email
    UserMailer.welcome(self).deliver_later
  end

  def allocate_first_badge
    FirstBadgeAllocationService.new(self).call
  end

  def send_admin_notification
    AdminMailer.new_user(self).deliver_later
  end

  def allocate_ai_feedback_credits_points_and_level
    update(
      points: 0,
      level: 0,
      ai_feedback_credits: self.premium_user? ? DEFAULT_AI_CREDITS_ALLOCATION : 0,
    )
  end

  def check_duplicate_name_and_email
    if self.first_name == self.last_name
      errors.add(:base, "Invalid user with duplicate first_name and last_name create attempt with email #{self.email}")
      Rails.logger.error "Invalid user with duplicate first_name and last_name create attempt with email #{self.email}"

      throw :abort
    end

    if self.name.empty?
      errors.add(:base, "Invalid user with empty name create attempt with email #{self.email}")
      Rails.logger.error "Invalid user with empty name create attempt with email #{self.email}"

      throw :abort
    end

    if self.last_name.empty?
      errors.add(:base, "Invalid user with empty last_name create attempt with email #{self.email}")
      Rails.logger.error "Invalid user with empty last_name create attempt with email #{self.email}"

      throw :abort
    end

    if (self.name.downcase == self.email.downcase)
      errors.add(:base, "Invalid user create attempt with email #{self.email}")
      Rails.logger.error "Invalid user create attempt with email #{self.email}"

      throw :abort
    end
  end
end