class Exercise < ApplicationRecord
  include MemoryInspirationExerciseConcern

  belongs_to :user

  has_one_attached :photo

  enum category: {
    homework_for_life: 0,
    first_last_best_worst: 1,
    crash_and_burn: 2,
    random_word: 3,
    prompt_inspiration: 4,
    copy_work: 5,
    memory_inspiration: 6,
  }

  validates :note, presence: true
  validates :note_date, presence: true
  validates :category, presence: true

  after_create :allocate_first_exercise_badge, :update_points_and_level, :add_to_weaviate
  after_update :update_in_weaviate, if: :saved_change_to_note?
  before_destroy :queue_remove_from_weaviate

  # Scope for getting exercises eligible for Weaviate search
  scope :searchable, -> { where(category: WeaviateServices::SEARCHABLE_CATEGORIES) }

  private

  def allocate_first_exercise_badge
    if self.user.exercises.count == 1
      FirstExerciseBadgeAllocationService.new(self.user).call
    end
  end

  def update_points_and_level
    Rails.logger.info "Allocating points to #{self.user.email} for completing #{self.category} exercise"

    PointsAndLevelAllocationService.new(self.user, points: 1).call
  end
  
  # Add newly created exercise to Weaviate if it's in a searchable category
  def add_to_weaviate
    return unless WeaviateServices::SEARCHABLE_CATEGORIES.include?(category)
    
    # Add to Weaviate in a background process to avoid slowing down the response
    Rails.logger.info "Adding exercise ID #{id} to Weaviate for semantic search"
    
    # Use a background job if available, otherwise do it synchronously
    if defined?(AddExerciseToWeaviateJob)
      AddExerciseToWeaviateJob.perform_later(id)
    else
      WeaviateServices.add_exercise_to_weaviate(self)
    end
  rescue => e
    # Don't let Weaviate errors affect the user experience
    Rails.logger.error "Error adding exercise to Weaviate: #{e.message}"
  end
  
  # Update exercise in Weaviate when the note content changes
  def update_in_weaviate
    return unless WeaviateServices::SEARCHABLE_CATEGORIES.include?(category)
    
    Rails.logger.info "Updating exercise ID #{id} in Weaviate after content change"
    
    # Use the background job for updates as well
    AddExerciseToWeaviateJob.perform_later(id)
  rescue => e
    # Don't let Weaviate errors affect the user experience
    Rails.logger.error "Error updating exercise in Weaviate: #{e.message}"
  end
  
  # Queue a job to remove the exercise from Weaviate when it's destroyed
  def queue_remove_from_weaviate
    return unless WeaviateServices::SEARCHABLE_CATEGORIES.include?(category)
    
    # We need to capture the exercise attributes before it's destroyed
    exercise_attributes = {
      "id" => id,
      "user_id" => user_id,
      "note" => note,
      "category" => category
    }
    
    # Queue the background job to remove from Weaviate
    Rails.logger.info "Queuing job to remove exercise ID #{id} from Weaviate"
    RemoveExerciseFromWeaviateJob.perform_later(exercise_attributes)
  rescue => e
    # Don't let Weaviate errors affect the user experience
    Rails.logger.error "Error queuing removal from Weaviate: #{e.message}"
  end
end