class Feedback < ApplicationRecord
  belongs_to :story
  
  before_save :sanitize_content
  
  # Add a getter method that returns sanitized content
  def sanitized_content
    return nil if content.nil?
    
    # Replace escape sequences that cause problems
    content.gsub(/\\"/,'"')  # Replace \" with simple "
           .gsub(/\\'/,"'")  # Replace \' with simple '
           .gsub(/\\n/,"\n") # Replace \n with actual newlines
           .gsub(/\\t/,"\t") # Replace \t with actual tabs
  end
  
  private
  
  def sanitize_content
    return if content.nil?
    
    # Replace escape sequences that cause problems in the actual stored content
    self.content = content.gsub(/\\"/,'"')  # Replace \" with simple "
                          .gsub(/\\'/,"'")  # Replace \' with simple '
                          .gsub(/\\n/,"\n") # Replace \n with actual newlines
                          .gsub(/\\t/,"\t") # Replace \t with actual tabs
  end
end
