class Story < ApplicationRecord
  attr_accessor :ai_feedback
  extend FriendlyId
  friendly_id :slug_candidates, use: :slugged

  def slug_candidates
    [
      :base_slug,
      -> { "#{base_slug}-#{SecureRandom.random_number(100)}" } # Generates a simple number (0-99) for uniqueness
    ]
  end

  include PromptInspirationConcern

  belongs_to :user

  has_many :feedbacks, dependent: :destroy

  after_create :allocate_first_story_badge
  after_create :update_points_and_level

  enum category: {
    general: 0,
    introduction: 1,
    origin: 2,
    linked_in: 3,
  }

  scope :published, -> { where(published: true) }

  private

  def base_slug
    return title.parameterize if title.present?

    return "#{user.name.parameterize}-#{category}" if user.present?

    "untitled-story"
  end

  def allocate_first_story_badge
    if self.user.stories.count == 1
      FirstStoryBadgeAllocationService.new(self.user).call
    end
  end

  def update_points_and_level
    Rails.logger.info "Allocating points to #{self.user.email} for creating a story"

    PointsAndLevelAllocationService.new(self.user, points: 3).call
  end
end