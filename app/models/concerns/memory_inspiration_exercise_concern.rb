module MemoryInspirationExerciseConcern
  extend ActiveSupport::Concern

  included do
    MEMORY_INSPIRATION_PROMPT = [
      "Did someone from your past pop up in your life unexpectedly?",
      "Did you hear a story from a friend or loved one that you had never heard before?",
      "Pay attention to your text messages this week. Did anyone text you something worth capturing?",
      "Take a look at what you're wearing each day this week. Does anything you're wearing contain a story?",
      "Did you do something that you would never tell anyone about? Did you think something that you would never tell anyone about?",
      "Did you react to something in the news this week? Your reactions and feelings about moments in history are often worthy of a story.",
      "Did someone see the world differently than you today? Did they give you a glimpse or understanding of the previously unknown world?",
      "Did something annoy you recently? Pay attention to the things that annoy you this week. Strong emotions make for excellent stories.",
      "Does a restaurant in your neighborhood hold find memories for you? Capture those memories from the past and present that make worthy stories.",
      "Take note of the things around you. Objects contain stories. Does the couch in your living room, the rocking chair on your porch, or your bed contain any stories?",
      "Did a memorable “What if?” cross your mind this week? “What if” questions — when not asked by pesky children who can't stand an uncertain future — are often clues to our curiosities, anxieties, and fears.",
      "Take note of the decisions you make this week. Often, the decisions we make impact our future, but it's impossible to predict which decision will result in extraordinary or horrendous or unforgettable results.",
    ]
  end
end