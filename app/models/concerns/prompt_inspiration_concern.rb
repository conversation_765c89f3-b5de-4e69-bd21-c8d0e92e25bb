module PromptInspirationConcern
  extend ActiveSupport::Concern

  included do
    PROMPT = [
      "Write a story about something unexpected that happened in the last three months.",
      "Write about the first day that you met your teacher. What did you think your teacher was going to be like? What was he or she actually like?",
      "Write a story about a pet that you have or an animal that you have loved. What kind of impact did that animal have on your life?",
      "Write a story about one of your family traditions. What does this tradition mean to you? Do you know when your family began this tradition?",
      "Write a story about a mistake that you made in the past. How did you feel when you made the mistake? What were the circumstances surrounding the incident? What did you learn from this mistake?",
      "Write about a dream that you had recently. What do you think the dream meant?",
      "Describe a time in which you faced a dilemma with a friendship. What was the problem? How did you handle it? Did you ask for help in solving the problem?",
      "Think about your favorite hobby or pastime. Write a story about how you discovered this hobby and why you decided to stick with it.",
      "What is the first item that you ever purchased with your own money? Write about how you earned the money and why you chose to buy that item. What does that item mean to you?",
      "Write a personal story about a memory that is difficult for you. Include information and insight as to why that was a difficult time in your life, and how you have worked to overcome that challenge.",
      "Write a story about a time when you tried something new. What did you try? How did you feel before you tried it? How did you feel afterward?",
      "Write a story about a special moment that you had with one of your role models. Why does this memory mean so much to you?"
    ]
  end
end