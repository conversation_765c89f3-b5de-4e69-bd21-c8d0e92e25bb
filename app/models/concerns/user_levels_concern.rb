module UserLevelsConcern
  extend ActiveSupport::Concern

  included do
    LEVEL_POINTS_AND_CREDITS_MAPPING = [
      {
        points: 0,
        credits: 0,
      },
      {
        points: 1,
        credits: 3,
      },
      {
        points: 5,
        credits: 2,
      },
      {
        points: 10,
        credits: 2,
      },
      {
        points: 50,
        credits: 3,
      },
      {
        points: 100,
        credits: 3,
      },
      {
        points: 200,
        credits: 5,
      },
      {
        points: 500,
        credits: 5,
      },
      {
        points: 750,
        credits: 5,
      },
      {
        points: 1000,
        credits: 5,
      },
    ]
  end
end