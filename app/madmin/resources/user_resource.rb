class UserResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :email
  attribute :first_name
  attribute :last_name
  attribute :premium_subscriber, form: false, index: false
  attribute :created_at, form: false
  attribute :last_sign_in_at, form: false
  attribute :ai_feedback_credits
  attribute :avatar, index: false
  attribute :goal
  attribute :practice_frequency
  attribute :practice_time_of_day
  attribute :admin

  # Associations
  attribute :services
  attribute :exercises
  attribute :badges
  attribute :stories

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end
