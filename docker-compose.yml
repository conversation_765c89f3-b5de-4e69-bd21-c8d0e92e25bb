services:
  redis:
    image: redis:7 # Use a specific Redis version
    ports:
      - "6379:6379" # Expose port if you need to connect from host
    volumes:
      - redis_data:/data # Persist Redis data (optional)

  db:
    image: postgres:15 # Use a specific PostgreSQL version
    volumes:
      - postgres_data:/var/lib/postgresql/data # Persist database data
    environment:
      POSTGRES_DB: storycoach_development
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD} # Load from .env file
    ports:
      - "5432:5432" # Expose port if you need to connect from host

  web:
    build: .
    command: ["./bin/dev"] # Use Procfile.dev command
    volumes:
      - .:/rails # Mount the app code
      - /rails/node_modules # Prevent host node_modules from overwriting container node_modules
      - gem_cache:/usr/local/bundle/gems # Persist gems
    env_file:
      - .env # Load environment variables from .env file
    ports:
      - "3000:3000"
    environment:
      RAILS_ENV: development
      # REDIS_URL is now loaded from .env via env_file
      DATABASE_URL: postgresql://postgres:${DB_PASSWORD}@db:5432/storycoach_development # Load password from .env file
      # Add any other necessary environment variables here
      # RAILS_MASTER_KEY should be loaded via .env file
    depends_on:
      - redis
      - db # Add dependency on the db service
    stdin_open: true # Allows attaching to the container (e.g., for binding.pry)
    tty: true        # Allows attaching to the container

volumes:
  redis_data: # Define the redis volume
  postgres_data: # Define the postgres volume
  gem_cache:
# node_modules: # Define if mounting node_modules
